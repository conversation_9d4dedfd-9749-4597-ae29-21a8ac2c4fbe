# Database (Neon.tech PostgreSQL)
DATABASE_URL=your_neon_database_url

# Email (Resend)
RESEND_API_KEY=your_resend_api_key
RESEND_EMAIL=your_verified_email

# Authentication (Google OAuth)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret


NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret // Generate here: https://auth-secret-gen.vercel.app/

# Better-auth
BETTER_AUTH_URL=http://localhost:3000 #Base URL of your app
BETTER_AUTH_SECRET=00c5c41ef7abdf593f9374bf31177f5e


# File Storage (AWS S3)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=your_aws_region
S3_UPLOAD_BUCKET=your_bucket_name

# Rate Limiting (Upstash Redis)
UPSTASH_REDIS_REST_URL=your_upstash_url
UPSTASH_REDIS_REST_TOKEN=your_upstash_token

# Payments (Stripe)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBGOOK=your_stripe_webhook_secret
STRIPE_STARTER_PRICE_ID=
STRIPE_PRO_PRICE_ID=

# App Encryption (used for encrypting and decrypting)
ENCRYPTION_KEY=your_encryption_key
NEXT_PUBLIC_APP_URL=http://localhost:3000 (for dev)