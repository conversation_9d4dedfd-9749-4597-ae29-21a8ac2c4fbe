{
  "$schema": "https://json.schemastore.org/eslintrc",
  "root": true,
  "extends": [
    "next/core-web-vitals",
    "prettier",
    "next/typescript",
    // "plugin:react-hooks/recommended",
    // "plugin:tailwindcss/recommended",
    "plugin:import/recommended",
    "plugin:import/typescript"
  ],
  "plugins": ["import"],
  "overrides": [{ "files": ["*.ts", "*.tsx"], "parser": "@typescript-eslint/parser" }],
  "settings": {
    "tailwindcss": { "callees": ["cn"], "config": "tailwind.config.ts" },
    "next": { "rootDir": true }
  },
  "rules": {
    "no-unused-vars": "warn",
    "no-undef": "warn",
    "no-console": "error",
    "no-alert": "error",
    "quotes": ["warn", "double"],
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/no-empty-object-type": "warn",
    "@typescript-eslint/no-unused-vars": "warn",
    // Disallow relative parent imports
    "no-restricted-imports": ["error", { "patterns": ["../*", "./*"] }]
  }
}
