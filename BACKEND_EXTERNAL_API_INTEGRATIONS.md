# External API Integrations
## Abstract Integration System for Affiliate Networks

This document outlines the abstract integration system that allows easy addition of new affiliate network APIs while maintaining consistency and reliability.

## 🏗️ Architecture Overview

### Abstract Base Classes
The system uses abstract base classes to define common interfaces that all network integrations must implement, ensuring consistency and easy extensibility.

```python
# integrations/base.py
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import asyncio
import aiohttp
from datetime import datetime

class AuthType(Enum):
    API_KEY = "api_key"
    OAUTH2 = "oauth2"
    BEARER_TOKEN = "bearer_token"
    BASIC_AUTH = "basic_auth"

@dataclass
class NetworkConfig:
    """Configuration for affiliate network"""
    name: str
    slug: str
    base_url: str
    auth_type: AuthType
    auth_config: Dict[str, Any]
    rate_limit: int = 1000
    timeout: int = 30
    retry_attempts: int = 3

@dataclass
class ProgramData:
    """Standardized program data structure"""
    external_id: str
    name: str
    brand: str
    description: str
    category: str
    commission_type: str
    commission_rate: float
    commission_structure: Dict[str, Any]
    cookie_duration: int
    payout_terms: str
    restrictions: Dict[str, Any]
    status: str
    min_payout: float
    avg_earnings_per_click: float
    conversion_rate: float
    logo_url: Optional[str] = None
    website_url: Optional[str] = None
    terms_url: Optional[str] = None
    tags: List[str] = None

@dataclass
class CommissionData:
    """Standardized commission data structure"""
    external_transaction_id: str
    program_external_id: str
    amount: float
    currency: str
    commission_type: str
    transaction_date: datetime
    order_value: Optional[float] = None
    commission_rate: Optional[float] = None
    status: str = "pending"

class BaseNetworkIntegration(ABC):
    """Abstract base class for all affiliate network integrations"""
    
    def __init__(self, config: NetworkConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self._auth_headers: Dict[str, str] = {}
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout)
        )
        await self.authenticate()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    @abstractmethod
    async def authenticate(self) -> bool:
        """Authenticate with the affiliate network"""
        pass
    
    @abstractmethod
    async def get_programs(self, **filters) -> List[ProgramData]:
        """Fetch affiliate programs from the network"""
        pass
    
    @abstractmethod
    async def get_program_details(self, program_id: str) -> Optional[ProgramData]:
        """Get detailed information about a specific program"""
        pass
    
    @abstractmethod
    async def apply_to_program(self, program_id: str, application_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply to join an affiliate program"""
        pass
    
    @abstractmethod
    async def get_commissions(self, date_from: datetime, date_to: datetime, **filters) -> List[CommissionData]:
        """Fetch commission data for a date range"""
        pass
    
    @abstractmethod
    async def generate_tracking_link(self, program_id: str, destination_url: str, **params) -> str:
        """Generate a tracking link for a program"""
        pass
    
    @abstractmethod
    async def get_click_data(self, date_from: datetime, date_to: datetime, **filters) -> List[Dict[str, Any]]:
        """Fetch click tracking data"""
        pass
    
    # Common utility methods
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make authenticated HTTP request with retry logic"""
        url = f"{self.config.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        headers = {**self._auth_headers, **kwargs.pop('headers', {})}
        
        for attempt in range(self.config.retry_attempts):
            try:
                async with self.session.request(method, url, headers=headers, **kwargs) as response:
                    if response.status == 429:  # Rate limited
                        await asyncio.sleep(2 ** attempt)
                        continue
                    
                    response.raise_for_status()
                    return await response.json()
                    
            except aiohttp.ClientError as e:
                if attempt == self.config.retry_attempts - 1:
                    raise NetworkIntegrationError(f"Request failed after {self.config.retry_attempts} attempts: {e}")
                await asyncio.sleep(2 ** attempt)
    
    def _normalize_program_data(self, raw_data: Dict[str, Any]) -> ProgramData:
        """Convert network-specific program data to standardized format"""
        # This method should be overridden by each integration
        # to handle network-specific data transformation
        raise NotImplementedError("Each integration must implement data normalization")
    
    def _normalize_commission_data(self, raw_data: Dict[str, Any]) -> CommissionData:
        """Convert network-specific commission data to standardized format"""
        # This method should be overridden by each integration
        raise NotImplementedError("Each integration must implement commission normalization")

class NetworkIntegrationError(Exception):
    """Custom exception for network integration errors"""
    pass
```

## 🔌 Specific Network Implementations

### AWIN Integration
```python
# integrations/awin.py
from .base import BaseNetworkIntegration, ProgramData, CommissionData, AuthType
from datetime import datetime
from typing import List, Dict, Any, Optional

class AWINIntegration(BaseNetworkIntegration):
    """AWIN affiliate network integration"""
    
    async def authenticate(self) -> bool:
        """Authenticate using API key"""
        api_key = self.config.auth_config.get('api_key')
        if not api_key:
            raise NetworkIntegrationError("AWIN API key not provided")
        
        self._auth_headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        # Test authentication
        try:
            await self._make_request('GET', '/publishers/me')
            return True
        except Exception as e:
            raise NetworkIntegrationError(f"AWIN authentication failed: {e}")
    
    async def get_programs(self, **filters) -> List[ProgramData]:
        """Fetch AWIN affiliate programs"""
        params = {
            'relationship': filters.get('relationship', 'joined'),
            'limit': filters.get('limit', 100),
            'offset': filters.get('offset', 0)
        }
        
        if filters.get('category'):
            params['category'] = filters['category']
        
        response = await self._make_request('GET', '/publishers/programmes', params=params)
        
        programs = []
        for program_data in response.get('programmes', []):
            programs.append(self._normalize_program_data(program_data))
        
        return programs
    
    async def get_program_details(self, program_id: str) -> Optional[ProgramData]:
        """Get detailed AWIN program information"""
        try:
            response = await self._make_request('GET', f'/publishers/programmes/{program_id}')
            return self._normalize_program_data(response)
        except Exception:
            return None
    
    async def apply_to_program(self, program_id: str, application_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply to AWIN program"""
        payload = {
            'programmeId': program_id,
            'message': application_data.get('message', ''),
            'websiteUrl': application_data.get('website_url', ''),
            'promotionMethod': application_data.get('promotion_method', 'website')
        }
        
        response = await self._make_request('POST', '/publishers/programme-requests', json=payload)
        
        return {
            'status': 'pending',
            'application_id': response.get('requestId'),
            'message': 'Application submitted successfully'
        }
    
    async def get_commissions(self, date_from: datetime, date_to: datetime, **filters) -> List[CommissionData]:
        """Fetch AWIN commission data"""
        params = {
            'startDate': date_from.strftime('%Y-%m-%d'),
            'endDate': date_to.strftime('%Y-%m-%d'),
            'timezone': filters.get('timezone', 'UTC')
        }
        
        if filters.get('program_id'):
            params['advertiserId'] = filters['program_id']
        
        response = await self._make_request('GET', '/publishers/transactions', params=params)
        
        commissions = []
        for transaction in response.get('transactions', []):
            commissions.append(self._normalize_commission_data(transaction))
        
        return commissions
    
    async def generate_tracking_link(self, program_id: str, destination_url: str, **params) -> str:
        """Generate AWIN tracking link"""
        tracking_params = {
            'advertiserId': program_id,
            'url': destination_url,
            'clickRef': params.get('click_ref', ''),
            'clickRef2': params.get('click_ref2', ''),
            'deeplink': 'true'
        }
        
        response = await self._make_request('GET', '/publishers/creative-links', params=tracking_params)
        return response.get('url', destination_url)
    
    async def get_click_data(self, date_from: datetime, date_to: datetime, **filters) -> List[Dict[str, Any]]:
        """Fetch AWIN click data"""
        params = {
            'startDate': date_from.strftime('%Y-%m-%d'),
            'endDate': date_to.strftime('%Y-%m-%d')
        }
        
        response = await self._make_request('GET', '/publishers/clicks', params=params)
        return response.get('clicks', [])
    
    def _normalize_program_data(self, raw_data: Dict[str, Any]) -> ProgramData:
        """Convert AWIN program data to standardized format"""
        return ProgramData(
            external_id=str(raw_data.get('id')),
            name=raw_data.get('name', ''),
            brand=raw_data.get('advertiser', {}).get('name', ''),
            description=raw_data.get('description', ''),
            category=raw_data.get('primaryCategory', {}).get('name', ''),
            commission_type='percentage',  # AWIN typically uses percentage
            commission_rate=float(raw_data.get('commissionRate', 0)),
            commission_structure=raw_data.get('commissionStructure', {}),
            cookie_duration=int(raw_data.get('cookieLength', 30)),
            payout_terms=raw_data.get('paymentTerms', ''),
            restrictions=raw_data.get('restrictions', {}),
            status=raw_data.get('status', 'active').lower(),
            min_payout=float(raw_data.get('minimumPayment', 0)),
            avg_earnings_per_click=float(raw_data.get('avgEpc', 0)),
            conversion_rate=float(raw_data.get('conversionRate', 0)),
            logo_url=raw_data.get('advertiser', {}).get('logoUrl'),
            website_url=raw_data.get('advertiser', {}).get('websiteUrl'),
            terms_url=raw_data.get('termsUrl'),
            tags=raw_data.get('categories', [])
        )
    
    def _normalize_commission_data(self, raw_data: Dict[str, Any]) -> CommissionData:
        """Convert AWIN commission data to standardized format"""
        return CommissionData(
            external_transaction_id=str(raw_data.get('id')),
            program_external_id=str(raw_data.get('advertiserId')),
            amount=float(raw_data.get('commissionAmount', 0)),
            currency=raw_data.get('currency', 'USD'),
            commission_type='conversion',
            transaction_date=datetime.fromisoformat(raw_data.get('transactionDate')),
            order_value=float(raw_data.get('saleAmount', 0)) if raw_data.get('saleAmount') else None,
            commission_rate=float(raw_data.get('commissionRate', 0)),
            status=raw_data.get('commissionStatus', 'pending').lower()
        )
```

### CJ Affiliate Integration
```python
# integrations/cj_affiliate.py
from .base import BaseNetworkIntegration, ProgramData, CommissionData, AuthType
from datetime import datetime
from typing import List, Dict, Any, Optional
import base64

class CJAffiliateIntegration(BaseNetworkIntegration):
    """CJ Affiliate (Commission Junction) integration"""
    
    async def authenticate(self) -> bool:
        """Authenticate using OAuth2 or API key"""
        if self.config.auth_type == AuthType.OAUTH2:
            return await self._oauth2_authenticate()
        else:
            return await self._api_key_authenticate()
    
    async def _api_key_authenticate(self) -> bool:
        """Authenticate using CJ API key"""
        api_key = self.config.auth_config.get('api_key')
        if not api_key:
            raise NetworkIntegrationError("CJ API key not provided")
        
        self._auth_headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        try:
            await self._make_request('GET', '/v3/publishers/me')
            return True
        except Exception as e:
            raise NetworkIntegrationError(f"CJ authentication failed: {e}")
    
    async def _oauth2_authenticate(self) -> bool:
        """Authenticate using OAuth2"""
        # Implementation for OAuth2 flow
        # This would involve token exchange and refresh logic
        pass
    
    async def get_programs(self, **filters) -> List[ProgramData]:
        """Fetch CJ affiliate programs"""
        params = {
            'records-per-page': filters.get('limit', 100),
            'page-number': filters.get('page', 1)
        }
        
        if filters.get('category'):
            params['category'] = filters['category']
        if filters.get('keywords'):
            params['keywords'] = filters['keywords']
        
        response = await self._make_request('GET', '/v3/advertisers', params=params)
        
        programs = []
        for advertiser in response.get('data', []):
            programs.append(self._normalize_program_data(advertiser))
        
        return programs
    
    async def get_program_details(self, program_id: str) -> Optional[ProgramData]:
        """Get detailed CJ program information"""
        try:
            response = await self._make_request('GET', f'/v3/advertisers/{program_id}')
            return self._normalize_program_data(response.get('data', {}))
        except Exception:
            return None
    
    async def apply_to_program(self, program_id: str, application_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply to CJ program"""
        # CJ typically requires manual application through their interface
        # This would return information about the application process
        return {
            'status': 'manual_application_required',
            'application_url': f'https://members.cj.com/member/publisher/application/advertiser/{program_id}',
            'message': 'Please complete application through CJ interface'
        }
    
    async def get_commissions(self, date_from: datetime, date_to: datetime, **filters) -> List[CommissionData]:
        """Fetch CJ commission data"""
        params = {
            'date-type': 'transaction',
            'start-date': date_from.strftime('%Y-%m-%d'),
            'end-date': date_to.strftime('%Y-%m-%d')
        }
        
        if filters.get('program_id'):
            params['advertiser-ids'] = filters['program_id']
        
        response = await self._make_request('GET', '/v3/commissions', params=params)
        
        commissions = []
        for commission in response.get('data', []):
            commissions.append(self._normalize_commission_data(commission))
        
        return commissions
    
    async def generate_tracking_link(self, program_id: str, destination_url: str, **params) -> str:
        """Generate CJ tracking link"""
        # CJ uses a different approach for link generation
        tracking_params = {
            'website-id': params.get('website_id'),
            'advertiser-id': program_id,
            'destination': destination_url
        }
        
        response = await self._make_request('GET', '/v3/links', params=tracking_params)
        return response.get('data', {}).get('link-url', destination_url)
    
    async def get_click_data(self, date_from: datetime, date_to: datetime, **filters) -> List[Dict[str, Any]]:
        """Fetch CJ click data"""
        params = {
            'start-date': date_from.strftime('%Y-%m-%d'),
            'end-date': date_to.strftime('%Y-%m-%d')
        }
        
        response = await self._make_request('GET', '/v3/publisher-clicks', params=params)
        return response.get('data', [])
    
    def _normalize_program_data(self, raw_data: Dict[str, Any]) -> ProgramData:
        """Convert CJ program data to standardized format"""
        return ProgramData(
            external_id=str(raw_data.get('advertiser-id')),
            name=raw_data.get('advertiser-name', ''),
            brand=raw_data.get('advertiser-name', ''),
            description=raw_data.get('program-description', ''),
            category=raw_data.get('primary-category', {}).get('parent', ''),
            commission_type='percentage',
            commission_rate=float(raw_data.get('commission-rate', 0)),
            commission_structure=raw_data.get('commission-structure', {}),
            cookie_duration=int(raw_data.get('cookie-duration', 30)),
            payout_terms=raw_data.get('payment-terms', ''),
            restrictions=raw_data.get('restrictions', {}),
            status=raw_data.get('relationship-status', 'available').lower(),
            min_payout=float(raw_data.get('minimum-payout', 0)),
            avg_earnings_per_click=float(raw_data.get('epc-7-day', 0)),
            conversion_rate=float(raw_data.get('conversion-rate', 0)),
            logo_url=raw_data.get('logo-url'),
            website_url=raw_data.get('website-url'),
            terms_url=raw_data.get('terms-url'),
            tags=raw_data.get('categories', [])
        )
    
    def _normalize_commission_data(self, raw_data: Dict[str, Any]) -> CommissionData:
        """Convert CJ commission data to standardized format"""
        return CommissionData(
            external_transaction_id=str(raw_data.get('commission-id')),
            program_external_id=str(raw_data.get('advertiser-id')),
            amount=float(raw_data.get('commission-amount', 0)),
            currency=raw_data.get('currency', 'USD'),
            commission_type='conversion',
            transaction_date=datetime.fromisoformat(raw_data.get('transaction-date')),
            order_value=float(raw_data.get('order-total', 0)) if raw_data.get('order-total') else None,
            commission_rate=float(raw_data.get('commission-rate', 0)),
            status=raw_data.get('commission-status', 'pending').lower()
        )
```

## 🏭 Integration Factory and Manager

### Integration Factory
```python
# integrations/factory.py
from typing import Dict, Type
from .base import BaseNetworkIntegration, NetworkConfig
from .awin import AWINIntegration
from .cj_affiliate import CJAffiliateIntegration
from .rakuten import RakutenIntegration
from .impact import ImpactIntegration

class IntegrationFactory:
    """Factory for creating network integration instances"""
    
    _integrations: Dict[str, Type[BaseNetworkIntegration]] = {
        'awin': AWINIntegration,
        'cj-affiliate': CJAffiliateIntegration,
        'rakuten': RakutenIntegration,
        'impact': ImpactIntegration,
    }
    
    @classmethod
    def create_integration(cls, network_slug: str, config: NetworkConfig) -> BaseNetworkIntegration:
        """Create integration instance for specified network"""
        integration_class = cls._integrations.get(network_slug)
        if not integration_class:
            raise ValueError(f"No integration available for network: {network_slug}")
        
        return integration_class(config)
    
    @classmethod
    def register_integration(cls, network_slug: str, integration_class: Type[BaseNetworkIntegration]):
        """Register new integration class"""
        cls._integrations[network_slug] = integration_class
    
    @classmethod
    def get_available_networks(cls) -> List[str]:
        """Get list of available network integrations"""
        return list(cls._integrations.keys())
```

### Integration Manager
```python
# integrations/manager.py
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from .factory import IntegrationFactory
from .base import NetworkConfig, ProgramData, CommissionData, AuthType
from models import AffiliateNetwork
import asyncio

class IntegrationManager:
    """Manages all affiliate network integrations"""
    
    def __init__(self, db: Session):
        self.db = db
        self._integrations: Dict[str, BaseNetworkIntegration] = {}
    
    async def initialize_integrations(self):
        """Initialize all active network integrations"""
        networks = self.db.query(AffiliateNetwork).filter(AffiliateNetwork.is_active == True).all()
        
        for network in networks:
            try:
                config = NetworkConfig(
                    name=network.name,
                    slug=network.slug,
                    base_url=network.api_endpoint,
                    auth_type=AuthType(network.auth_type),
                    auth_config=network.auth_config,
                    rate_limit=network.rate_limit
                )
                
                integration = IntegrationFactory.create_integration(network.slug, config)
                self._integrations[network.slug] = integration
                
            except Exception as e:
                print(f"Failed to initialize integration for {network.name}: {e}")
    
    async def sync_programs(self, network_slug: Optional[str] = None) -> Dict[str, int]:
        """Sync programs from one or all networks"""
        results = {}
        
        networks_to_sync = [network_slug] if network_slug else list(self._integrations.keys())
        
        for slug in networks_to_sync:
            if slug not in self._integrations:
                continue
            
            try:
                async with self._integrations[slug] as integration:
                    programs = await integration.get_programs()
                    count = await self._save_programs(slug, programs)
                    results[slug] = count
                    
            except Exception as e:
                print(f"Failed to sync programs for {slug}: {e}")
                results[slug] = 0
        
        return results
    
    async def sync_commissions(self, network_slug: str, date_from: datetime, date_to: datetime) -> int:
        """Sync commissions from specific network"""
        if network_slug not in self._integrations:
            raise ValueError(f"Integration not available for {network_slug}")
        
        try:
            async with self._integrations[network_slug] as integration:
                commissions = await integration.get_commissions(date_from, date_to)
                return await self._save_commissions(network_slug, commissions)
                
        except Exception as e:
            print(f"Failed to sync commissions for {network_slug}: {e}")
            return 0
    
    async def generate_tracking_link(self, network_slug: str, program_id: str, destination_url: str, **params) -> str:
        """Generate tracking link through network integration"""
        if network_slug not in self._integrations:
            raise ValueError(f"Integration not available for {network_slug}")
        
        async with self._integrations[network_slug] as integration:
            return await integration.generate_tracking_link(program_id, destination_url, **params)
    
    async def _save_programs(self, network_slug: str, programs: List[ProgramData]) -> int:
        """Save programs to database"""
        # Implementation to save programs to database
        # This would involve creating/updating AffiliateProgram records
        pass
    
    async def _save_commissions(self, network_slug: str, commissions: List[CommissionData]) -> int:
        """Save commissions to database"""
        # Implementation to save commissions to database
        # This would involve creating Commission records
        pass
```

## 🔄 Usage Examples

### Adding New Network Integration
```python
# integrations/new_network.py
from .base import BaseNetworkIntegration, ProgramData, CommissionData

class NewNetworkIntegration(BaseNetworkIntegration):
    """Integration for a new affiliate network"""
    
    async def authenticate(self) -> bool:
        # Implement authentication logic
        pass
    
    async def get_programs(self, **filters) -> List[ProgramData]:
        # Implement program fetching logic
        pass
    
    # Implement other required methods...

# Register the new integration
from integrations.factory import IntegrationFactory
IntegrationFactory.register_integration('new-network', NewNetworkIntegration)
```

### Using the Integration System
```python
# In your FastAPI endpoints
from integrations.manager import IntegrationManager

@app.post("/api/v1/admin/sync-programs")
async def sync_programs(network: Optional[str] = None, db: Session = Depends(get_db)):
    manager = IntegrationManager(db)
    await manager.initialize_integrations()
    
    results = await manager.sync_programs(network)
    return {"synced_programs": results}

@app.post("/api/v1/links/generate")
async def generate_link(
    program_id: str,
    destination_url: str,
    network_slug: str,
    db: Session = Depends(get_db)
):
    manager = IntegrationManager(db)
    await manager.initialize_integrations()
    
    tracking_url = await manager.generate_tracking_link(
        network_slug, program_id, destination_url
    )
    
    return {"tracking_url": tracking_url}
```

## 🎯 Benefits of This Architecture

### 1. **Easy Extensibility**
- Add new networks by implementing the base interface
- No changes needed to existing code
- Consistent API across all integrations

### 2. **Standardized Data**
- All networks return data in the same format
- Easy to work with in the application layer
- Consistent database storage

### 3. **Error Handling**
- Built-in retry logic and error handling
- Network-specific error handling can be added
- Graceful degradation when networks are unavailable

### 4. **Rate Limiting**
- Automatic rate limiting per network
- Configurable limits per integration
- Built-in backoff strategies

### 5. **Authentication Management**
- Support for multiple auth types
- Automatic token refresh for OAuth2
- Secure credential storage

### 6. **Testing and Mocking**
- Easy to mock integrations for testing
- Consistent interface for unit tests
- Integration tests can use real or mock data

This abstract system makes it incredibly easy to add new affiliate networks in the future while maintaining consistency and reliability across all integrations.
