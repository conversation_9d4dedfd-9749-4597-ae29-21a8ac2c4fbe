# Backend Test Data Setup
## Automated Database Population for Testing

This guide provides scripts to populate the database with realistic test data for development and testing purposes.

## 📋 Test Data Script

Create `scripts/populate_test_data.sql`:

```sql
-- ============================================================================
-- AFFILIATE MIDDLEWARE PLATFORM - TEST DATA POPULATION
-- ============================================================================

-- Clear existing test data (be careful in production!)
-- TRUNCATE TABLE webhook_endpoints, api_keys, payouts, commissions, click_events, tracking_links, campaigns, user_program_enrollments, affiliate_programs, affiliate_networks CASCADE;

-- ============================================================================
-- 1. AFFILIATE NETWORKS TEST DATA
-- ============================================================================

INSERT INTO affiliate_networks (id, name, slug, description, api_endpoint, auth_type, auth_config, is_active, supported_features, rate_limit, logo_url, website_url) VALUES
(uuid_generate_v4(), 'AWIN', 'awin', 'Global affiliate network with 15,000+ advertisers', 'https://api.awin.com/v1', 'api_key', '{"api_key_header": "Authorization", "token_prefix": "Bearer"}', true, '{"tracking": true, "reporting": true, "real_time": false}', 1000, 'https://example.com/logos/awin.png', 'https://www.awin.com'),

(uuid_generate_v4(), 'CJ Affiliate', 'cj-affiliate', 'Commission Junction - Leading performance marketing network', 'https://api.cj.com/v3', 'oauth2', '{"client_id": "your_client_id", "scope": "read_write"}', true, '{"tracking": true, "reporting": true, "real_time": true}', 500, 'https://example.com/logos/cj.png', 'https://www.cj.com'),

(uuid_generate_v4(), 'Rakuten Advertising', 'rakuten', 'Global leader in affiliate marketing', 'https://api.rakutenadvertising.com/v2', 'oauth2', '{"client_id": "your_client_id", "scope": "affiliate"}', true, '{"tracking": true, "reporting": true, "real_time": true}', 750, 'https://example.com/logos/rakuten.png', 'https://rakutenadvertising.com'),

(uuid_generate_v4(), 'Impact Radius', 'impact', 'Partnership automation platform', 'https://api.impact.com/v1', 'bearer_token', '{"token_header": "Authorization"}', true, '{"tracking": true, "reporting": true, "real_time": true, "fraud_detection": true}', 2000, 'https://example.com/logos/impact.png', 'https://impact.com'),

(uuid_generate_v4(), 'TradeDoubler', 'tradedoubler', 'European affiliate marketing network', 'https://api.tradedoubler.com/1.0', 'api_key', '{"api_key_param": "token"}', true, '{"tracking": true, "reporting": true, "real_time": false}', 800, 'https://example.com/logos/tradedoubler.png', 'https://www.tradedoubler.com'),

(uuid_generate_v4(), 'PartnerStack', 'partnerstack', 'B2B partner ecosystem platform', 'https://api.partnerstack.com/v2', 'bearer_token', '{"token_header": "X-API-Key"}', true, '{"tracking": true, "reporting": true, "real_time": true, "b2b_focus": true}', 1500, 'https://example.com/logos/partnerstack.png', 'https://partnerstack.com');

-- ============================================================================
-- 2. AFFILIATE PROGRAMS TEST DATA
-- ============================================================================

-- Get network IDs for reference
WITH network_ids AS (
    SELECT id, slug FROM affiliate_networks
)
INSERT INTO affiliate_programs (
    network_id, external_id, name, brand, description, category, 
    commission_type, commission_rate, commission_structure, cookie_duration,
    payout_terms, status, min_payout, avg_earnings_per_click, conversion_rate,
    logo_url, website_url, terms_url, tags
)
SELECT 
    n.id,
    'PROG_' || generate_random_uuid()::text,
    program_data.name,
    program_data.brand,
    program_data.description,
    program_data.category,
    program_data.commission_type::commission_type_enum,
    program_data.commission_rate,
    program_data.commission_structure::jsonb,
    program_data.cookie_duration,
    program_data.payout_terms,
    'active'::program_status_enum,
    program_data.min_payout,
    program_data.avg_epc,
    program_data.conversion_rate,
    program_data.logo_url,
    program_data.website_url,
    program_data.terms_url,
    program_data.tags::jsonb
FROM network_ids n
CROSS JOIN (
    VALUES 
    -- AWIN Programs
    ('Nike', 'Nike', 'Global sportswear and athletic equipment', 'Fashion & Sports', 'percentage', 5.50, '{"base_rate": 5.5, "premium_rate": 7.0, "threshold": 1000}', 30, 'Net 30 days', 25.00, 0.0125, 0.0340, 'https://example.com/logos/nike.png', 'https://nike.com', 'https://nike.com/affiliate-terms', '["sports", "fashion", "footwear"]'),
    
    ('Amazon Associates', 'Amazon', 'Worlds largest online marketplace', 'E-commerce', 'tiered', 3.00, '{"rates": [{"category": "electronics", "rate": 2.5}, {"category": "fashion", "rate": 4.0}]}', 24, 'Net 60 days', 10.00, 0.0089, 0.0280, 'https://example.com/logos/amazon.png', 'https://amazon.com', 'https://amazon.com/associates-terms', '["ecommerce", "retail", "marketplace"]'),
    
    ('Booking.com', 'Booking', 'Global accommodation booking platform', 'Travel', 'fixed', 25.00, '{"base_commission": 25, "premium_commission": 35, "luxury_commission": 50}', 30, 'Net 30 days', 50.00, 0.0450, 0.0520, 'https://example.com/logos/booking.png', 'https://booking.com', 'https://booking.com/affiliate-terms', '["travel", "hotels", "accommodation"]'),
    
    -- CJ Affiliate Programs
    ('Best Buy', 'Best Buy', 'Electronics and technology retailer', 'Electronics', 'percentage', 2.50, '{"base_rate": 2.5, "mobile_rate": 3.0}', 7, 'Net 45 days', 20.00, 0.0095, 0.0310, 'https://example.com/logos/bestbuy.png', 'https://bestbuy.com', 'https://bestbuy.com/affiliate-terms', '["electronics", "technology", "retail"]'),
    
    ('Expedia', 'Expedia', 'Online travel booking platform', 'Travel', 'percentage', 4.00, '{"hotel_rate": 4.0, "flight_rate": 2.0, "package_rate": 5.0}', 45, 'Net 30 days', 30.00, 0.0380, 0.0480, 'https://example.com/logos/expedia.png', 'https://expedia.com', 'https://expedia.com/affiliate-terms', '["travel", "flights", "hotels"]'),
    
    -- Rakuten Programs
    ('Walmart', 'Walmart', 'American multinational retail corporation', 'Retail', 'percentage', 1.50, '{"base_rate": 1.5, "grocery_rate": 2.0}', 3, 'Net 60 days', 25.00, 0.0067, 0.0290, 'https://example.com/logos/walmart.png', 'https://walmart.com', 'https://walmart.com/affiliate-terms', '["retail", "grocery", "marketplace"]'),
    
    ('Sephora', 'Sephora', 'Beauty and cosmetics retailer', 'Beauty', 'percentage', 6.00, '{"base_rate": 6.0, "premium_rate": 8.0}', 30, 'Net 30 days', 15.00, 0.0180, 0.0420, 'https://example.com/logos/sephora.png', 'https://sephora.com', 'https://sephora.com/affiliate-terms', '["beauty", "cosmetics", "skincare"]')
) AS program_data(name, brand, description, category, commission_type, commission_rate, commission_structure, cookie_duration, payout_terms, min_payout, avg_epc, conversion_rate, logo_url, website_url, terms_url, tags)
WHERE (n.slug = 'awin' AND program_data.brand IN ('Nike', 'Amazon', 'Booking'))
   OR (n.slug = 'cj-affiliate' AND program_data.brand IN ('Best Buy', 'Expedia'))
   OR (n.slug = 'rakuten' AND program_data.brand IN ('Walmart', 'Sephora'));

-- ============================================================================
-- 3. ENHANCED USER TEST DATA
-- ============================================================================

-- Update existing users with affiliate-specific data
UPDATE users SET 
    user_type = (ARRAY['individual', 'agency', 'developer']::user_type_enum[])[floor(random() * 3 + 1)],
    kyc_status = (ARRAY['verified', 'pending', 'not_required']::kyc_status_enum[])[floor(random() * 3 + 1)],
    affiliate_id = 'AFF_' || upper(substring(md5(random()::text) from 1 for 8)),
    payout_method = (ARRAY['stripe', 'paypal', 'bank_transfer']::payout_method_enum[])[floor(random() * 3 + 1)],
    payout_details = jsonb_build_object(
        'account_holder', name || ' ' || "lastName",
        'verified', true,
        'last_updated', CURRENT_TIMESTAMP
    ),
    total_earnings = round((random() * 5000)::numeric, 2),
    pending_earnings = round((random() * 500)::numeric, 2),
    lifetime_clicks = floor(random() * 10000),
    lifetime_conversions = floor(random() * 500)
WHERE id IN (SELECT id FROM users LIMIT 10);

-- ============================================================================
-- 4. USER PROGRAM ENROLLMENTS TEST DATA
-- ============================================================================

-- Enroll users in various affiliate programs
WITH user_program_combinations AS (
    SELECT 
        u.id as user_id,
        p.id as program_id,
        w.id as workspace_id,
        row_number() OVER () as rn
    FROM users u
    CROSS JOIN affiliate_programs p
    LEFT JOIN workspaces w ON w."ownerId" = u.id
    WHERE u.user_type IS NOT NULL
    ORDER BY random()
    LIMIT 50
)
INSERT INTO user_program_enrollments (
    user_id, program_id, workspace_id, status, application_date, 
    approval_date, tracking_id, custom_commission_rate
)
SELECT 
    user_id,
    program_id,
    workspace_id,
    (ARRAY['approved', 'pending', 'approved', 'approved']::enrollment_status_enum[])[floor(random() * 4 + 1)],
    CURRENT_TIMESTAMP - (random() * interval '30 days'),
    CASE 
        WHEN random() > 0.3 THEN CURRENT_TIMESTAMP - (random() * interval '20 days')
        ELSE NULL 
    END,
    'TRK_' || upper(substring(md5(random()::text) from 1 for 12)),
    CASE 
        WHEN random() > 0.8 THEN round((random() * 2 + 3)::numeric, 2)
        ELSE NULL 
    END
FROM user_program_combinations;

-- ============================================================================
-- 5. CAMPAIGNS TEST DATA
-- ============================================================================

INSERT INTO campaigns (
    user_id, workspace_id, name, description, start_date, end_date,
    budget, target_clicks, target_conversions, status, tags, utm_source, utm_medium, utm_campaign
)
SELECT 
    u.id,
    w.id,
    campaign_data.name,
    campaign_data.description,
    CURRENT_TIMESTAMP - (random() * interval '60 days'),
    CASE 
        WHEN random() > 0.5 THEN CURRENT_TIMESTAMP + (random() * interval '30 days')
        ELSE NULL 
    END,
    round((random() * 2000 + 500)::numeric, 2),
    floor(random() * 5000 + 1000),
    floor(random() * 200 + 50),
    (ARRAY['active', 'paused', 'active', 'active']::campaign_status_enum[])[floor(random() * 4 + 1)],
    campaign_data.tags::jsonb,
    campaign_data.utm_source,
    campaign_data.utm_medium,
    campaign_data.utm_campaign
FROM users u
LEFT JOIN workspaces w ON w."ownerId" = u.id
CROSS JOIN (
    VALUES 
    ('Summer Fashion Campaign', 'Promoting summer clothing and accessories', '["fashion", "summer", "clothing"]', 'website', 'affiliate', 'summer_fashion_2024'),
    ('Tech Gadgets Promotion', 'Latest technology and gadget reviews', '["technology", "gadgets", "reviews"]', 'blog', 'content', 'tech_gadgets_q1'),
    ('Travel Deals Campaign', 'Best travel deals and destination guides', '["travel", "deals", "destinations"]', 'social', 'paid', 'travel_deals_spring'),
    ('Beauty Product Reviews', 'Honest beauty product reviews and tutorials', '["beauty", "reviews", "tutorials"]', 'youtube', 'video', 'beauty_reviews_2024'),
    ('Home & Garden Spring', 'Spring home improvement and gardening', '["home", "garden", "spring"]', 'pinterest', 'organic', 'home_garden_spring')
) AS campaign_data(name, description, tags, utm_source, utm_medium, utm_campaign)
WHERE u.user_type IS NOT NULL
LIMIT 25;

-- ============================================================================
-- 6. TRACKING LINKS TEST DATA
-- ============================================================================

INSERT INTO tracking_links (
    user_id, program_id, workspace_id, campaign_id, original_url, tracking_url,
    short_code, link_type, custom_parameters, is_active, expires_at,
    click_count, conversion_count, total_commission
)
SELECT 
    upe.user_id,
    upe.program_id,
    upe.workspace_id,
    c.id,
    'https://' || ap.website_url || '/product/' || floor(random() * 1000 + 1),
    'https://track.affiliate-platform.com/' || substring(md5(random()::text) from 1 for 16),
    upper(substring(md5(random()::text) from 1 for 8)),
    (ARRAY['direct', 'social', 'widget', 'iframe']::link_type_enum[])[floor(random() * 4 + 1)],
    jsonb_build_object(
        'utm_source', 'affiliate',
        'utm_medium', 'link',
        'utm_campaign', c.utm_campaign,
        'custom_param', 'value_' || floor(random() * 100)
    ),
    random() > 0.1,
    CASE 
        WHEN random() > 0.7 THEN CURRENT_TIMESTAMP + (random() * interval '90 days')
        ELSE NULL 
    END,
    floor(random() * 1000),
    floor(random() * 50),
    round((random() * 200)::numeric, 2)
FROM user_program_enrollments upe
JOIN affiliate_programs ap ON upe.program_id = ap.id
LEFT JOIN campaigns c ON c.user_id = upe.user_id
WHERE upe.status = 'approved'
ORDER BY random()
LIMIT 100;

-- ============================================================================
-- 7. CLICK EVENTS TEST DATA
-- ============================================================================

INSERT INTO click_events (
    tracking_link_id, ip_address, user_agent, referrer, country, region, city,
    device, browser, os, clicked_at, conversion_at, commission_amount,
    conversion_value, status, session_id
)
SELECT 
    tl.id,
    ('192.168.' || floor(random() * 255) || '.' || floor(random() * 255))::inet,
    (ARRAY[
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
    ])[floor(random() * 3 + 1)],
    (ARRAY[
        'https://google.com/search',
        'https://facebook.com',
        'https://twitter.com',
        'direct'
    ])[floor(random() * 4 + 1)],
    (ARRAY['US', 'GB', 'CA', 'AU', 'DE', 'FR', 'ES', 'IT'])[floor(random() * 8 + 1)],
    'Region_' || floor(random() * 50 + 1),
    'City_' || floor(random() * 100 + 1),
    (ARRAY['desktop', 'mobile', 'tablet'])[floor(random() * 3 + 1)],
    (ARRAY['Chrome', 'Firefox', 'Safari', 'Edge'])[floor(random() * 4 + 1)],
    (ARRAY['Windows', 'macOS', 'iOS', 'Android', 'Linux'])[floor(random() * 5 + 1)],
    CURRENT_TIMESTAMP - (random() * interval '30 days'),
    CASE 
        WHEN random() > 0.85 THEN CURRENT_TIMESTAMP - (random() * interval '25 days')
        ELSE NULL 
    END,
    CASE 
        WHEN random() > 0.85 THEN round((random() * 50 + 5)::numeric, 2)
        ELSE NULL 
    END,
    CASE 
        WHEN random() > 0.85 THEN round((random() * 500 + 50)::numeric, 2)
        ELSE NULL 
    END,
    (ARRAY['confirmed', 'pending', 'confirmed', 'confirmed']::click_status_enum[])[floor(random() * 4 + 1)],
    'sess_' || substring(md5(random()::text) from 1 for 16)
FROM tracking_links tl
WHERE tl.is_active = true
ORDER BY random()
LIMIT 500;

-- ============================================================================
-- 8. COMMISSIONS TEST DATA
-- ============================================================================

INSERT INTO commissions (
    user_id, program_id, tracking_link_id, click_event_id, amount, currency,
    commission_type, status, transaction_date, confirmed_at, commission_rate,
    order_value, external_transaction_id
)
SELECT 
    ce.tracking_link_id,
    tl.program_id,
    tl.id,
    ce.id,
    ce.commission_amount,
    'USD',
    'conversion',
    (ARRAY['confirmed', 'pending', 'confirmed', 'paid']::commission_status_enum[])[floor(random() * 4 + 1)],
    ce.clicked_at,
    CASE 
        WHEN random() > 0.3 THEN ce.conversion_at + (random() * interval '7 days')
        ELSE NULL 
    END,
    round((random() * 5 + 2)::numeric, 2),
    ce.conversion_value,
    'TXN_' || upper(substring(md5(random()::text) from 1 for 16))
FROM click_events ce
JOIN tracking_links tl ON ce.tracking_link_id = tl.id
WHERE ce.commission_amount IS NOT NULL
AND ce.conversion_at IS NOT NULL;

-- ============================================================================
-- 9. API KEYS TEST DATA
-- ============================================================================

INSERT INTO api_keys (
    user_id, workspace_id, name, key_hash, permissions, is_active,
    usage_count, rate_limit, expires_at
)
SELECT 
    u.id,
    w.id,
    'API Key ' || generate_random_uuid()::text,
    'sk_' || substring(md5(random()::text) from 1 for 32),
    '["read:programs", "read:analytics", "write:links"]'::jsonb,
    random() > 0.2,
    floor(random() * 1000),
    (ARRAY[1000, 2500, 5000, 10000])[floor(random() * 4 + 1)],
    CURRENT_TIMESTAMP + (random() * interval '365 days')
FROM users u
LEFT JOIN workspaces w ON w."ownerId" = u.id
WHERE u.user_type = 'developer'
LIMIT 15;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify data was inserted correctly
SELECT 'Affiliate Networks' as table_name, count(*) as record_count FROM affiliate_networks
UNION ALL
SELECT 'Affiliate Programs', count(*) FROM affiliate_programs
UNION ALL
SELECT 'User Program Enrollments', count(*) FROM user_program_enrollments
UNION ALL
SELECT 'Campaigns', count(*) FROM campaigns
UNION ALL
SELECT 'Tracking Links', count(*) FROM tracking_links
UNION ALL
SELECT 'Click Events', count(*) FROM click_events
UNION ALL
SELECT 'Commissions', count(*) FROM commissions
UNION ALL
SELECT 'API Keys', count(*) FROM api_keys;
```

## 🚀 Execution Instructions

### 1. Run the Test Data Script

```bash
# Execute the test data population script
psql -U your_username -d your_database -f scripts/populate_test_data.sql

# Or with environment variables
PGPASSWORD=your_password psql -h localhost -U your_username -d your_database -f scripts/populate_test_data.sql
```

### 2. Python Script for Dynamic Test Data

Create `scripts/generate_test_data.py`:

```python
import asyncio
import random
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
import os

# Database connection
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql+asyncpg://user:password@localhost/affiliate_db")
engine = create_async_engine(DATABASE_URL)
AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

async def generate_additional_test_data():
    """Generate additional test data programmatically"""
    async with AsyncSessionLocal() as session:
        # Add more sophisticated test data generation here
        print("Generating additional test data...")
        
        # Example: Generate more click events
        await generate_click_events(session, count=1000)
        
        # Example: Generate commission data
        await generate_commissions(session, count=200)
        
        await session.commit()
        print("Test data generation completed!")

async def generate_click_events(session: AsyncSession, count: int):
    """Generate realistic click events"""
    # Implementation for generating click events
    pass

async def generate_commissions(session: AsyncSession, count: int):
    """Generate commission records"""
    # Implementation for generating commissions
    pass

if __name__ == "__main__":
    asyncio.run(generate_additional_test_data())
```

### 3. Run Python Test Data Generator

```bash
# Install dependencies
pip install sqlalchemy[asyncio] asyncpg

# Run the Python script
python scripts/generate_test_data.py
```

## 📊 Test Data Summary

After running the scripts, you'll have:

- **6 Affiliate Networks** (AWIN, CJ, Rakuten, Impact, TradeDoubler, PartnerStack)
- **7 Affiliate Programs** across different categories
- **50 User Program Enrollments** with various statuses
- **25 Campaigns** with realistic data
- **100 Tracking Links** with analytics data
- **500 Click Events** with geographic and device data
- **Commission Records** based on conversions
- **15 API Keys** for developer users

## 🔍 Data Verification Queries

```sql
-- Check data distribution
SELECT 
    an.name as network,
    COUNT(ap.id) as programs,
    COUNT(DISTINCT upe.user_id) as enrolled_users
FROM affiliate_networks an
LEFT JOIN affiliate_programs ap ON an.id = ap.network_id
LEFT JOIN user_program_enrollments upe ON ap.id = upe.program_id
GROUP BY an.id, an.name;

-- Check tracking performance
SELECT 
    ap.brand,
    COUNT(tl.id) as total_links,
    SUM(tl.click_count) as total_clicks,
    SUM(tl.conversion_count) as total_conversions,
    ROUND(AVG(tl.total_commission), 2) as avg_commission
FROM affiliate_programs ap
LEFT JOIN tracking_links tl ON ap.id = tl.program_id
GROUP BY ap.id, ap.brand
ORDER BY total_clicks DESC;
```

## 🧹 Cleanup Script

Create `scripts/cleanup_test_data.sql` for removing test data:

```sql
-- WARNING: This will delete all test data
TRUNCATE TABLE
    webhook_endpoints,
    api_keys,
    payouts,
    commissions,
    click_events,
    tracking_links,
    campaigns,
    user_program_enrollments,
    affiliate_programs,
    affiliate_networks
CASCADE;

-- Reset user affiliate fields
UPDATE users SET
    user_type = 'individual',
    kyc_status = 'not_required',
    affiliate_id = NULL,
    total_earnings = 0.00,
    pending_earnings = 0.00,
    lifetime_clicks = 0,
    lifetime_conversions = 0;
```

## 🔗 Integration with Advanced Features

### **External API Testing**
The test data is designed to work with the external API integration system:
- See `BACKEND_EXTERNAL_API_INTEGRATIONS.md` for network integration testing
- Test data includes realistic external IDs and API responses
- Mock data supports all affiliate networks (AWIN, CJ, Rakuten, etc.)

### **Background Job Testing**
Test data supports background job processing:
- See `BACKEND_BACKGROUND_JOBS.md` for Celery task testing
- Commission sync jobs can process the test commission data
- Report generation jobs work with the test analytics data
- Webhook delivery jobs use the test webhook endpoints

### **Real-time Feature Testing**
Test data enables real-time feature testing:
- See `BACKEND_REALTIME_FEATURES.md` for WebSocket testing
- Click events trigger real-time notifications
- Commission updates broadcast to connected users
- Live analytics update with test data changes

### **Docker Environment Testing**
Test data works seamlessly with Docker:
- See `BACKEND_DOCKER_DEPLOYMENT.md` for containerized testing
- Automated test data population in Docker containers
- Consistent test environment across development and CI/CD

## 📚 Related Documentation

### **Core Setup**
- `BACKEND_DATABASE_SETUP.md` - Database schema and setup
- `BACKEND_API_SPECIFICATION.md` - API endpoints that use this test data

### **Advanced Features**
- `BACKEND_EXTERNAL_API_INTEGRATIONS.md` - Network integration testing
- `BACKEND_REALTIME_FEATURES.md` - WebSocket testing with test data
- `BACKEND_BACKGROUND_JOBS.md` - Celery task testing
- `BACKEND_DOCKER_DEPLOYMENT.md` - Containerized test environment

### **Architecture**
- `COMPREHENSIVE_ENTITIES_DOCUMENTATION.md` - Complete system overview
- `DATABASE_SCHEMA_SUMMARY.md` - Entity relationships

## 🎯 Testing Scenarios Supported

### **User Journey Testing**
- New user registration and onboarding
- Program discovery and enrollment
- Campaign creation and link generation
- Click tracking and commission earning
- Payout processing and withdrawal

### **Integration Testing**
- Affiliate network API synchronization
- Webhook delivery and retry logic
- Email notification sending
- Real-time update broadcasting
- Background job processing

### **Performance Testing**
- High-volume click event processing
- Large report generation
- Concurrent user analytics
- Database query optimization
- Cache performance validation

### **Security Testing**
- API key authentication
- Rate limiting validation
- Data access control
- Webhook signature verification
- User permission testing

This comprehensive test data setup ensures all features of the affiliate platform can be thoroughly tested in a realistic environment.
