# Background Jobs with Celery & Redis
## Asynchronous Task Processing for Affiliate Platform

This document explains the background job system using Celery and Redis for handling time-consuming and scheduled tasks.

## 🎯 Why Background Jobs?

### **Essential Use Cases:**

1. **Report Generation**
   - Large CSV/PDF reports can take minutes to generate
   - Users shouldn't wait for complex analytics queries
   - Email delivery of completed reports

2. **Webhook Delivery**
   - Reliable delivery with retry logic
   - Handle webhook endpoint failures gracefully
   - Batch webhook deliveries for efficiency

3. **Data Synchronization**
   - Sync affiliate programs from external APIs
   - Update commission data from networks
   - Periodic data cleanup and maintenance

4. **Email Notifications**
   - Bulk email campaigns
   - Transactional emails (welcome, notifications)
   - Email queue management

5. **Analytics Processing**
   - Heavy analytics calculations
   - Data aggregation for dashboards
   - Performance metric calculations

## 🏗️ Celery Configuration

### Basic Setup
```python
# celery_app.py
from celery import Celery
from kombu import Queue
import os

# Redis configuration
REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')

# Create Celery app
celery_app = Celery(
    'affiliate_platform',
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=[
        'tasks.reports',
        'tasks.webhooks',
        'tasks.sync',
        'tasks.emails',
        'tasks.analytics'
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task routing
    task_routes={
        'tasks.reports.*': {'queue': 'reports'},
        'tasks.webhooks.*': {'queue': 'webhooks'},
        'tasks.sync.*': {'queue': 'sync'},
        'tasks.emails.*': {'queue': 'emails'},
        'tasks.analytics.*': {'queue': 'analytics'},
    },
    
    # Queue definitions
    task_queues=(
        Queue('default', routing_key='default'),
        Queue('reports', routing_key='reports'),
        Queue('webhooks', routing_key='webhooks'),
        Queue('sync', routing_key='sync'),
        Queue('emails', routing_key='emails'),
        Queue('analytics', routing_key='analytics'),
    ),
    
    # Task settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    
    # Worker settings
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # Retry settings
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'sync-programs-daily': {
            'task': 'tasks.sync.sync_all_programs',
            'schedule': 86400.0,  # 24 hours
        },
        'sync-commissions-hourly': {
            'task': 'tasks.sync.sync_recent_commissions',
            'schedule': 3600.0,  # 1 hour
        },
        'cleanup-old-reports': {
            'task': 'tasks.reports.cleanup_old_reports',
            'schedule': 86400.0,  # 24 hours
        },
        'process-pending-payouts': {
            'task': 'tasks.payouts.process_pending_payouts',
            'schedule': 3600.0,  # 1 hour
        },
    },
)

# Task base class with common functionality
class BaseTask(celery_app.Task):
    """Base task class with common functionality"""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure"""
        print(f"Task {task_id} failed: {exc}")
        # Log to monitoring system
        # Send alert if critical task
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Handle task retry"""
        print(f"Task {task_id} retrying: {exc}")
    
    def on_success(self, retval, task_id, args, kwargs):
        """Handle task success"""
        print(f"Task {task_id} completed successfully")

# Set default base class
celery_app.Task = BaseTask
```

### Task Definitions

#### Report Generation Tasks
```python
# tasks/reports.py
from celery import current_task
from celery_app import celery_app
from sqlalchemy.orm import Session
from database import SessionLocal
from models import User, Commission, ClickEvent, TrackingLink
import pandas as pd
import io
import boto3
from datetime import datetime, timedelta
import uuid

@celery_app.task(bind=True, queue='reports')
def generate_commission_report(self, user_id: str, date_from: str, date_to: str, format: str = 'csv'):
    """Generate commission report for user"""
    try:
        # Update task status
        self.update_state(state='PROGRESS', meta={'progress': 10, 'status': 'Starting report generation'})
        
        db = SessionLocal()
        
        # Query commission data
        self.update_state(state='PROGRESS', meta={'progress': 30, 'status': 'Querying commission data'})
        
        commissions = db.query(Commission).filter(
            Commission.user_id == user_id,
            Commission.transaction_date >= date_from,
            Commission.transaction_date <= date_to
        ).all()
        
        # Convert to DataFrame
        self.update_state(state='PROGRESS', meta={'progress': 50, 'status': 'Processing data'})
        
        data = []
        for commission in commissions:
            data.append({
                'Date': commission.transaction_date.strftime('%Y-%m-%d'),
                'Program': commission.program.name,
                'Amount': commission.amount,
                'Currency': commission.currency,
                'Status': commission.status,
                'Order Value': commission.order_value,
                'Commission Rate': commission.commission_rate,
                'Transaction ID': commission.external_transaction_id
            })
        
        df = pd.DataFrame(data)
        
        # Generate file
        self.update_state(state='PROGRESS', meta={'progress': 70, 'status': 'Generating file'})
        
        if format == 'csv':
            file_content = df.to_csv(index=False)
            file_extension = 'csv'
            content_type = 'text/csv'
        elif format == 'excel':
            buffer = io.BytesIO()
            df.to_excel(buffer, index=False)
            file_content = buffer.getvalue()
            file_extension = 'xlsx'
            content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        
        # Upload to S3
        self.update_state(state='PROGRESS', meta={'progress': 90, 'status': 'Uploading file'})
        
        s3_client = boto3.client('s3')
        file_key = f"reports/{user_id}/{uuid.uuid4()}.{file_extension}"
        
        s3_client.put_object(
            Bucket='affiliate-platform-reports',
            Key=file_key,
            Body=file_content,
            ContentType=content_type,
            Expires=datetime.utcnow() + timedelta(days=7)  # 7 days expiry
        )
        
        # Generate download URL
        download_url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': 'affiliate-platform-reports', 'Key': file_key},
            ExpiresIn=604800  # 7 days
        )
        
        db.close()
        
        return {
            'status': 'completed',
            'download_url': download_url,
            'file_size': len(file_content),
            'record_count': len(data),
            'generated_at': datetime.utcnow().isoformat()
        }
        
    except Exception as exc:
        db.close()
        self.retry(exc=exc, countdown=60, max_retries=3)

@celery_app.task(queue='reports')
def cleanup_old_reports():
    """Clean up old report files from S3"""
    try:
        s3_client = boto3.client('s3')
        
        # List objects older than 7 days
        cutoff_date = datetime.utcnow() - timedelta(days=7)
        
        response = s3_client.list_objects_v2(
            Bucket='affiliate-platform-reports',
            Prefix='reports/'
        )
        
        deleted_count = 0
        for obj in response.get('Contents', []):
            if obj['LastModified'].replace(tzinfo=None) < cutoff_date:
                s3_client.delete_object(
                    Bucket='affiliate-platform-reports',
                    Key=obj['Key']
                )
                deleted_count += 1
        
        return {'deleted_files': deleted_count}
        
    except Exception as exc:
        print(f"Error cleaning up reports: {exc}")
        return {'error': str(exc)}
```

#### Webhook Delivery Tasks
```python
# tasks/webhooks.py
from celery_app import celery_app
from sqlalchemy.orm import Session
from database import SessionLocal
from models import WebhookEndpoint, WebhookDelivery
import aiohttp
import asyncio
import hmac
import hashlib
import json
from datetime import datetime

@celery_app.task(bind=True, queue='webhooks', max_retries=5)
def deliver_webhook(self, webhook_endpoint_id: str, event_type: str, payload: dict):
    """Deliver webhook with retry logic"""
    try:
        db = SessionLocal()
        
        webhook = db.query(WebhookEndpoint).filter(
            WebhookEndpoint.id == webhook_endpoint_id
        ).first()
        
        if not webhook or not webhook.is_active:
            return {'status': 'skipped', 'reason': 'webhook inactive'}
        
        # Check if event type is subscribed
        if event_type not in webhook.events:
            return {'status': 'skipped', 'reason': 'event not subscribed'}
        
        # Prepare webhook payload
        webhook_payload = {
            'event_type': event_type,
            'timestamp': datetime.utcnow().isoformat(),
            'data': payload
        }
        
        # Generate signature
        signature = generate_webhook_signature(
            json.dumps(webhook_payload, sort_keys=True),
            webhook.secret
        )
        
        # Deliver webhook
        response = asyncio.run(send_webhook_request(
            webhook.url,
            webhook_payload,
            signature
        ))
        
        # Record delivery
        delivery = WebhookDelivery(
            webhook_endpoint_id=webhook.id,
            event_type=event_type,
            payload=webhook_payload,
            response_status=response['status_code'],
            response_body=response['body'][:1000],  # Limit response body size
            delivered_at=datetime.utcnow()
        )
        db.add(delivery)
        
        # Update webhook stats
        if response['status_code'] >= 200 and response['status_code'] < 300:
            webhook.last_triggered_at = datetime.utcnow()
            webhook.failure_count = 0
        else:
            webhook.failure_count += 1
            
            # Disable webhook if too many failures
            if webhook.failure_count >= webhook.max_failures:
                webhook.is_active = False
        
        db.commit()
        db.close()
        
        if response['status_code'] >= 200 and response['status_code'] < 300:
            return {'status': 'delivered', 'response_code': response['status_code']}
        else:
            # Retry with exponential backoff
            raise Exception(f"Webhook delivery failed with status {response['status_code']}")
            
    except Exception as exc:
        db.close()
        
        # Exponential backoff: 1min, 2min, 4min, 8min, 16min
        countdown = 60 * (2 ** self.request.retries)
        self.retry(exc=exc, countdown=countdown)

async def send_webhook_request(url: str, payload: dict, signature: str) -> dict:
    """Send webhook HTTP request"""
    headers = {
        'Content-Type': 'application/json',
        'X-Webhook-Signature': signature,
        'User-Agent': 'AffiliateMiddleware/1.0'
    }
    
    timeout = aiohttp.ClientTimeout(total=30)
    
    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(url, json=payload, headers=headers) as response:
                body = await response.text()
                return {
                    'status_code': response.status,
                    'body': body
                }
    except Exception as e:
        return {
            'status_code': 0,
            'body': str(e)
        }

def generate_webhook_signature(payload: str, secret: str) -> str:
    """Generate HMAC signature for webhook"""
    return hmac.new(
        secret.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

@celery_app.task(queue='webhooks')
def batch_webhook_delivery(webhook_data_list: list):
    """Deliver multiple webhooks in batch"""
    results = []
    
    for webhook_data in webhook_data_list:
        result = deliver_webhook.delay(
            webhook_data['webhook_endpoint_id'],
            webhook_data['event_type'],
            webhook_data['payload']
        )
        results.append(result.id)
    
    return {'batch_size': len(webhook_data_list), 'task_ids': results}
```

#### Data Synchronization Tasks
```python
# tasks/sync.py
from celery_app import celery_app
from sqlalchemy.orm import Session
from database import SessionLocal
from models import AffiliateNetwork, AffiliateProgram, Commission
from integrations.manager import IntegrationManager
from datetime import datetime, timedelta

@celery_app.task(bind=True, queue='sync')
def sync_all_programs(self):
    """Sync programs from all active networks"""
    try:
        self.update_state(state='PROGRESS', meta={'progress': 10, 'status': 'Initializing'})
        
        db = SessionLocal()
        manager = IntegrationManager(db)
        
        self.update_state(state='PROGRESS', meta={'progress': 30, 'status': 'Initializing integrations'})
        
        # Initialize integrations
        asyncio.run(manager.initialize_integrations())
        
        self.update_state(state='PROGRESS', meta={'progress': 50, 'status': 'Syncing programs'})
        
        # Sync programs from all networks
        results = asyncio.run(manager.sync_programs())
        
        db.close()
        
        return {
            'status': 'completed',
            'synced_programs': results,
            'total_programs': sum(results.values()),
            'synced_at': datetime.utcnow().isoformat()
        }
        
    except Exception as exc:
        db.close()
        self.retry(exc=exc, countdown=300, max_retries=3)  # 5 minute delay

@celery_app.task(bind=True, queue='sync')
def sync_network_programs(self, network_slug: str):
    """Sync programs from specific network"""
    try:
        db = SessionLocal()
        manager = IntegrationManager(db)
        
        # Initialize integrations
        asyncio.run(manager.initialize_integrations())
        
        # Sync programs from specific network
        results = asyncio.run(manager.sync_programs(network_slug))
        
        db.close()
        
        return {
            'status': 'completed',
            'network': network_slug,
            'synced_programs': results.get(network_slug, 0),
            'synced_at': datetime.utcnow().isoformat()
        }
        
    except Exception as exc:
        db.close()
        self.retry(exc=exc, countdown=300, max_retries=3)

@celery_app.task(bind=True, queue='sync')
def sync_recent_commissions(self):
    """Sync commissions from last 24 hours"""
    try:
        db = SessionLocal()
        manager = IntegrationManager(db)
        
        # Initialize integrations
        asyncio.run(manager.initialize_integrations())
        
        # Sync commissions from last 24 hours
        date_from = datetime.utcnow() - timedelta(days=1)
        date_to = datetime.utcnow()
        
        total_synced = 0
        networks = db.query(AffiliateNetwork).filter(AffiliateNetwork.is_active == True).all()
        
        for network in networks:
            try:
                count = asyncio.run(manager.sync_commissions(
                    network.slug, date_from, date_to
                ))
                total_synced += count
            except Exception as e:
                print(f"Failed to sync commissions for {network.slug}: {e}")
        
        db.close()
        
        return {
            'status': 'completed',
            'total_synced': total_synced,
            'synced_at': datetime.utcnow().isoformat()
        }
        
    except Exception as exc:
        db.close()
        self.retry(exc=exc, countdown=300, max_retries=3)
```

#### Email Tasks
```python
# tasks/emails.py
from celery_app import celery_app
from email_service import EmailService
from models import User
from database import SessionLocal

@celery_app.task(queue='emails')
def send_welcome_email(user_id: str):
    """Send welcome email to new user"""
    try:
        db = SessionLocal()
        user = db.query(User).filter(User.id == user_id).first()
        
        if user:
            email_service = EmailService()
            email_service.send_welcome_email(user.email, user.name)
        
        db.close()
        return {'status': 'sent', 'user_id': user_id}
        
    except Exception as exc:
        print(f"Failed to send welcome email: {exc}")
        return {'status': 'failed', 'error': str(exc)}

@celery_app.task(queue='emails')
def send_commission_notification(user_id: str, commission_data: dict):
    """Send commission notification email"""
    try:
        db = SessionLocal()
        user = db.query(User).filter(User.id == user_id).first()
        
        if user and user.notification_settings.get('email_commissions', True):
            email_service = EmailService()
            email_service.send_commission_notification(
                user.email, 
                user.name, 
                commission_data
            )
        
        db.close()
        return {'status': 'sent', 'user_id': user_id}
        
    except Exception as exc:
        print(f"Failed to send commission notification: {exc}")
        return {'status': 'failed', 'error': str(exc)}

@celery_app.task(queue='emails')
def send_bulk_newsletter(user_ids: list, newsletter_data: dict):
    """Send newsletter to multiple users"""
    try:
        db = SessionLocal()
        email_service = EmailService()
        
        sent_count = 0
        failed_count = 0
        
        for user_id in user_ids:
            try:
                user = db.query(User).filter(User.id == user_id).first()
                if user and user.notification_settings.get('email_newsletter', True):
                    email_service.send_newsletter(user.email, user.name, newsletter_data)
                    sent_count += 1
            except Exception as e:
                print(f"Failed to send newsletter to user {user_id}: {e}")
                failed_count += 1
        
        db.close()
        
        return {
            'status': 'completed',
            'sent_count': sent_count,
            'failed_count': failed_count,
            'total_users': len(user_ids)
        }
        
    except Exception as exc:
        print(f"Bulk newsletter failed: {exc}")
        return {'status': 'failed', 'error': str(exc)}
```

## 🚀 Usage in FastAPI

### Triggering Background Tasks
```python
# In your FastAPI routes
from tasks.reports import generate_commission_report
from tasks.webhooks import deliver_webhook
from tasks.emails import send_welcome_email

@app.post("/api/v1/reports/generate")
async def create_report(
    report_request: ReportRequest,
    current_user: User = Depends(get_current_user)
):
    """Generate report in background"""
    
    # Start background task
    task = generate_commission_report.delay(
        user_id=current_user.id,
        date_from=report_request.date_from,
        date_to=report_request.date_to,
        format=report_request.format
    )
    
    return {
        "task_id": task.id,
        "status": "started",
        "message": "Report generation started"
    }

@app.get("/api/v1/reports/{task_id}/status")
async def get_report_status(task_id: str):
    """Get report generation status"""
    
    task = generate_commission_report.AsyncResult(task_id)
    
    if task.state == 'PENDING':
        response = {'status': 'pending'}
    elif task.state == 'PROGRESS':
        response = {
            'status': 'in_progress',
            'progress': task.info.get('progress', 0),
            'message': task.info.get('status', '')
        }
    elif task.state == 'SUCCESS':
        response = {
            'status': 'completed',
            'result': task.result
        }
    else:
        response = {
            'status': 'failed',
            'error': str(task.info)
        }
    
    return response

# Trigger webhook delivery
@app.post("/webhooks/trigger")
async def trigger_webhook(webhook_data: dict):
    """Trigger webhook delivery"""
    
    deliver_webhook.delay(
        webhook_endpoint_id=webhook_data['webhook_id'],
        event_type=webhook_data['event_type'],
        payload=webhook_data['payload']
    )
    
    return {"status": "webhook_queued"}
```

## 🔧 Running Celery Workers

### Start Workers
```bash
# Start default worker
celery -A celery_app worker --loglevel=info

# Start worker for specific queue
celery -A celery_app worker --loglevel=info -Q reports

# Start multiple workers for different queues
celery -A celery_app worker --loglevel=info -Q webhooks,emails

# Start worker with concurrency
celery -A celery_app worker --loglevel=info --concurrency=4
```

### Start Celery Beat (Scheduler)
```bash
# Start beat scheduler for periodic tasks
celery -A celery_app beat --loglevel=info
```

### Monitor with Flower
```bash
# Install flower
pip install flower

# Start flower monitoring
celery -A celery_app flower
```

## 📊 Monitoring and Management

### Task Monitoring
```python
# tasks/monitoring.py
from celery_app import celery_app
from celery import current_app

@celery_app.task(queue='monitoring')
def get_queue_stats():
    """Get queue statistics"""
    inspect = current_app.control.inspect()
    
    stats = {
        'active_tasks': inspect.active(),
        'scheduled_tasks': inspect.scheduled(),
        'reserved_tasks': inspect.reserved(),
        'queue_lengths': {}
    }
    
    # Get queue lengths from Redis
    from celery_app import celery_app
    redis_client = celery_app.broker_connection().default_channel.client
    
    queues = ['default', 'reports', 'webhooks', 'sync', 'emails', 'analytics']
    for queue in queues:
        stats['queue_lengths'][queue] = redis_client.llen(f'celery:{queue}')
    
    return stats

@celery_app.task(queue='monitoring')
def cleanup_failed_tasks():
    """Clean up failed tasks older than 24 hours"""
    # Implementation to clean up old failed tasks
    pass
```

This background job system provides robust, scalable task processing that's essential for a production affiliate platform. It handles the heavy lifting while keeping your API responses fast and your users happy.
