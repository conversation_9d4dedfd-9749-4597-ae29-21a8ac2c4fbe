import { Metadata } from "next"
import { HydrateClient, trpc } from "@/trpc/server"

import { ROUTES } from "@/lib/routes"
import { ItemsGrid } from "@/components/item/items-grid"
import { SectionWrapper } from "@/components/layout/section-wrapper"

export const metadata: Metadata = ROUTES.items.metadata

export const dynamic = "force-dynamic"

type ItemsPageProps = {
  params: Promise<{ slug: string }>
}

export default async function ItemsPage({ params }: ItemsPageProps) {
  const { slug } = await params

  void trpc.items.getMany.prefetch({ slug })

  return (
    <HydrateClient>
      <SectionWrapper>
        <ItemsGrid slug={slug} />
      </SectionWrapper>
    </HydrateClient>
  )
}
