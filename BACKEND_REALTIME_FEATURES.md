# Real-time Features with WebSockets
## Live Analytics and Notifications System

This document outlines the WebSocket implementation for real-time features in the affiliate platform, explaining when and why they're useful.

## 🎯 Why Real-time Features?

### **Utility and Benefits:**

1. **Live Analytics Dashboard**
   - Real-time click tracking and conversion updates
   - Live commission notifications as they happen
   - Instant performance metrics updates
   - Better user engagement with immediate feedback

2. **Instant Notifications**
   - Program approval/rejection notifications
   - Commission confirmations
   - Payout status updates
   - System alerts and maintenance notices

3. **Collaborative Features**
   - Team workspace updates
   - Shared campaign performance
   - Multi-user dashboard synchronization

4. **Competitive Advantage**
   - Immediate response to performance changes
   - Real-time optimization opportunities
   - Enhanced user experience vs traditional polling

## 🏗️ WebSocket Architecture

### Connection Manager
```python
# websocket/connection_manager.py
from typing import Dict, List, Set
from fastapi import WebSocket
import json
import asyncio
from datetime import datetime
import redis
import uuid

class ConnectionManager:
    """Manages WebSocket connections for real-time features"""
    
    def __init__(self):
        # Active connections by user_id
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # Connection metadata
        self.connection_metadata: Dict[WebSocket, Dict] = {}
        # Redis for multi-instance support
        self.redis = redis.Redis(host='localhost', port=6379, db=0)
        
    async def connect(self, websocket: WebSocket, user_id: str, connection_type: str = "dashboard"):
        """Accept new WebSocket connection"""
        await websocket.accept()
        
        # Add to active connections
        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        self.active_connections[user_id].add(websocket)
        
        # Store connection metadata
        self.connection_metadata[websocket] = {
            "user_id": user_id,
            "connection_type": connection_type,
            "connected_at": datetime.utcnow(),
            "connection_id": str(uuid.uuid4())
        }
        
        # Send welcome message
        await self.send_personal_message({
            "type": "connection_established",
            "message": "Connected to real-time updates",
            "connection_id": self.connection_metadata[websocket]["connection_id"]
        }, websocket)
        
        print(f"User {user_id} connected via WebSocket ({connection_type})")
    
    async def disconnect(self, websocket: WebSocket):
        """Handle WebSocket disconnection"""
        if websocket in self.connection_metadata:
            user_id = self.connection_metadata[websocket]["user_id"]
            
            # Remove from active connections
            if user_id in self.active_connections:
                self.active_connections[user_id].discard(websocket)
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]
            
            # Remove metadata
            del self.connection_metadata[websocket]
            print(f"User {user_id} disconnected from WebSocket")
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send message to specific WebSocket connection"""
        try:
            await websocket.send_text(json.dumps({
                **message,
                "timestamp": datetime.utcnow().isoformat()
            }))
        except Exception as e:
            print(f"Error sending message to WebSocket: {e}")
            await self.disconnect(websocket)
    
    async def send_to_user(self, user_id: str, message: dict):
        """Send message to all connections of a specific user"""
        if user_id in self.active_connections:
            disconnected = []
            for websocket in self.active_connections[user_id].copy():
                try:
                    await self.send_personal_message(message, websocket)
                except:
                    disconnected.append(websocket)
            
            # Clean up disconnected sockets
            for websocket in disconnected:
                await self.disconnect(websocket)
    
    async def send_to_workspace(self, workspace_id: str, message: dict):
        """Send message to all users in a workspace"""
        # This would require querying workspace members from database
        # and sending to all their active connections
        pass
    
    async def broadcast_to_all(self, message: dict):
        """Broadcast message to all connected users"""
        for user_id in list(self.active_connections.keys()):
            await self.send_to_user(user_id, message)
    
    async def get_connection_stats(self) -> dict:
        """Get current connection statistics"""
        total_connections = sum(len(connections) for connections in self.active_connections.values())
        return {
            "total_users": len(self.active_connections),
            "total_connections": total_connections,
            "connections_by_type": self._get_connections_by_type()
        }
    
    def _get_connections_by_type(self) -> dict:
        """Get connection count by type"""
        types = {}
        for websocket, metadata in self.connection_metadata.items():
            conn_type = metadata["connection_type"]
            types[conn_type] = types.get(conn_type, 0) + 1
        return types

# Global connection manager instance
manager = ConnectionManager()
```

### WebSocket Endpoints
```python
# websocket/endpoints.py
from fastapi import WebSocket, WebSocketDisconnect, Depends, HTTPException
from fastapi.routing import APIRouter
from .connection_manager import manager
from dependencies import get_current_user_from_token
import json

router = APIRouter()

@router.websocket("/ws/dashboard/{token}")
async def websocket_dashboard(websocket: WebSocket, token: str):
    """WebSocket endpoint for dashboard real-time updates"""
    try:
        # Verify token and get user
        user = await get_current_user_from_token(token)
        if not user:
            await websocket.close(code=4001, reason="Invalid token")
            return
        
        await manager.connect(websocket, user.id, "dashboard")
        
        try:
            while True:
                # Keep connection alive and handle incoming messages
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                if message.get("type") == "ping":
                    await manager.send_personal_message({"type": "pong"}, websocket)
                elif message.get("type") == "subscribe":
                    # Handle subscription to specific data streams
                    await handle_subscription(websocket, user.id, message)
                
        except WebSocketDisconnect:
            await manager.disconnect(websocket)
            
    except Exception as e:
        print(f"WebSocket error: {e}")
        await websocket.close(code=4000, reason="Internal error")

@router.websocket("/ws/analytics/{token}")
async def websocket_analytics(websocket: WebSocket, token: str):
    """WebSocket endpoint for real-time analytics"""
    try:
        user = await get_current_user_from_token(token)
        if not user:
            await websocket.close(code=4001, reason="Invalid token")
            return
        
        await manager.connect(websocket, user.id, "analytics")
        
        # Start sending real-time analytics data
        await start_analytics_stream(websocket, user.id)
        
    except WebSocketDisconnect:
        await manager.disconnect(websocket)
    except Exception as e:
        print(f"Analytics WebSocket error: {e}")
        await websocket.close(code=4000, reason="Internal error")

async def handle_subscription(websocket: WebSocket, user_id: str, message: dict):
    """Handle subscription requests"""
    subscription_type = message.get("subscription")
    
    if subscription_type == "live_clicks":
        # Subscribe to live click events
        await manager.send_personal_message({
            "type": "subscription_confirmed",
            "subscription": "live_clicks",
            "message": "Subscribed to live click events"
        }, websocket)
    
    elif subscription_type == "commission_updates":
        # Subscribe to commission updates
        await manager.send_personal_message({
            "type": "subscription_confirmed",
            "subscription": "commission_updates",
            "message": "Subscribed to commission updates"
        }, websocket)

async def start_analytics_stream(websocket: WebSocket, user_id: str):
    """Start streaming analytics data"""
    try:
        while True:
            # Get latest analytics data for user
            analytics_data = await get_real_time_analytics(user_id)
            
            await manager.send_personal_message({
                "type": "analytics_update",
                "data": analytics_data
            }, websocket)
            
            # Update every 5 seconds
            await asyncio.sleep(5)
            
    except WebSocketDisconnect:
        await manager.disconnect(websocket)
```

### Real-time Event Handlers
```python
# websocket/event_handlers.py
from .connection_manager import manager
from models import ClickEvent, Commission, Payout
from sqlalchemy.orm import Session
import asyncio

class RealTimeEventHandler:
    """Handles real-time events and broadcasts to connected clients"""
    
    @staticmethod
    async def handle_new_click(click_event: ClickEvent, db: Session):
        """Handle new click event"""
        # Get tracking link and user info
        tracking_link = click_event.tracking_link
        user_id = tracking_link.user_id
        
        # Prepare click data
        click_data = {
            "type": "new_click",
            "data": {
                "click_id": str(click_event.id),
                "tracking_link_id": str(tracking_link.id),
                "program_name": tracking_link.program.name,
                "country": click_event.country,
                "device": click_event.device,
                "referrer": click_event.referrer,
                "clicked_at": click_event.clicked_at.isoformat()
            }
        }
        
        # Send to user
        await manager.send_to_user(user_id, click_data)
        
        # Update live statistics
        await RealTimeEventHandler.update_live_stats(user_id, "click")
    
    @staticmethod
    async def handle_new_commission(commission: Commission, db: Session):
        """Handle new commission"""
        user_id = commission.user_id
        
        commission_data = {
            "type": "new_commission",
            "data": {
                "commission_id": str(commission.id),
                "amount": float(commission.amount),
                "currency": commission.currency,
                "program_name": commission.program.name,
                "status": commission.status,
                "transaction_date": commission.transaction_date.isoformat()
            }
        }
        
        # Send to user
        await manager.send_to_user(user_id, commission_data)
        
        # Update live statistics
        await RealTimeEventHandler.update_live_stats(user_id, "commission", commission.amount)
    
    @staticmethod
    async def handle_payout_update(payout: Payout, db: Session):
        """Handle payout status update"""
        user_id = payout.user_id
        
        payout_data = {
            "type": "payout_update",
            "data": {
                "payout_id": str(payout.id),
                "amount": float(payout.amount),
                "status": payout.status,
                "method": payout.method,
                "processed_at": payout.processed_at.isoformat() if payout.processed_at else None
            }
        }
        
        # Send to user
        await manager.send_to_user(user_id, payout_data)
    
    @staticmethod
    async def handle_program_status_change(enrollment_id: str, new_status: str, user_id: str):
        """Handle program enrollment status change"""
        status_data = {
            "type": "program_status_change",
            "data": {
                "enrollment_id": enrollment_id,
                "new_status": new_status,
                "message": f"Program application status changed to {new_status}"
            }
        }
        
        # Send to user
        await manager.send_to_user(user_id, status_data)
    
    @staticmethod
    async def update_live_stats(user_id: str, event_type: str, amount: float = 0):
        """Update and broadcast live statistics"""
        # Get updated stats from database
        stats = await get_user_live_stats(user_id)
        
        stats_data = {
            "type": "stats_update",
            "data": {
                "total_clicks_today": stats["clicks_today"],
                "total_commissions_today": stats["commissions_today"],
                "earnings_today": stats["earnings_today"],
                "conversion_rate": stats["conversion_rate"]
            }
        }
        
        # Send to user
        await manager.send_to_user(user_id, stats_data)

async def get_user_live_stats(user_id: str) -> dict:
    """Get live statistics for user"""
    # This would query the database for today's stats
    # Implementation would depend on your specific requirements
    return {
        "clicks_today": 0,
        "commissions_today": 0,
        "earnings_today": 0.0,
        "conversion_rate": 0.0
    }
```

### Integration with FastAPI Routes
```python
# In your existing API routes, add real-time notifications

# routes/tracking.py
from websocket.event_handlers import RealTimeEventHandler

@router.post("/track/{short_code}")
async def track_click(short_code: str, request: Request, db: Session = Depends(get_db)):
    """Handle click tracking with real-time updates"""
    # ... existing click tracking logic ...
    
    # Create click event
    click_event = ClickEvent(
        tracking_link_id=tracking_link.id,
        ip_address=request.client.host,
        user_agent=request.headers.get("user-agent"),
        # ... other fields
    )
    db.add(click_event)
    db.commit()
    
    # Send real-time notification
    await RealTimeEventHandler.handle_new_click(click_event, db)
    
    # ... rest of the logic

# routes/commissions.py
@router.post("/commissions/webhook")
async def commission_webhook(webhook_data: dict, db: Session = Depends(get_db)):
    """Handle commission webhook from affiliate networks"""
    # ... process webhook data ...
    
    # Create commission record
    commission = Commission(
        user_id=user_id,
        amount=webhook_data["amount"],
        # ... other fields
    )
    db.add(commission)
    db.commit()
    
    # Send real-time notification
    await RealTimeEventHandler.handle_new_commission(commission, db)
    
    return {"status": "processed"}
```

### Frontend WebSocket Client Example
```javascript
// Frontend WebSocket client example
class AffiliateWebSocket {
    constructor(token) {
        this.token = token;
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }
    
    connect() {
        const wsUrl = `ws://localhost:8000/ws/dashboard/${this.token}`;
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('Connected to real-time updates');
            this.reconnectAttempts = 0;
            
            // Subscribe to specific events
            this.subscribe('live_clicks');
            this.subscribe('commission_updates');
        };
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket connection closed');
            this.reconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }
    
    subscribe(eventType) {
        this.send({
            type: 'subscribe',
            subscription: eventType
        });
    }
    
    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    
    handleMessage(data) {
        switch (data.type) {
            case 'new_click':
                this.updateClickCounter(data.data);
                this.showNotification('New click received!');
                break;
                
            case 'new_commission':
                this.updateCommissionDisplay(data.data);
                this.showNotification(`New commission: $${data.data.amount}`);
                break;
                
            case 'stats_update':
                this.updateDashboardStats(data.data);
                break;
                
            case 'payout_update':
                this.updatePayoutStatus(data.data);
                break;
        }
    }
    
    reconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.connect();
            }, 2000 * this.reconnectAttempts);
        }
    }
    
    updateClickCounter(clickData) {
        // Update UI with new click data
        const clickCounter = document.getElementById('click-counter');
        if (clickCounter) {
            clickCounter.textContent = parseInt(clickCounter.textContent) + 1;
        }
    }
    
    updateCommissionDisplay(commissionData) {
        // Update UI with new commission
        const commissionList = document.getElementById('recent-commissions');
        if (commissionList) {
            const commissionElement = document.createElement('div');
            commissionElement.innerHTML = `
                <div class="commission-item">
                    <span>${commissionData.program_name}</span>
                    <span>$${commissionData.amount}</span>
                    <span>${new Date(commissionData.transaction_date).toLocaleString()}</span>
                </div>
            `;
            commissionList.prepend(commissionElement);
        }
    }
    
    showNotification(message) {
        // Show browser notification or in-app notification
        if (Notification.permission === 'granted') {
            new Notification('Affiliate Platform', { body: message });
        }
    }
}

// Usage
const affiliateWS = new AffiliateWebSocket(userToken);
affiliateWS.connect();
```

## 🎯 When to Use Real-time Features

### **Essential Use Cases:**
1. **Live Dashboard Updates** - For active campaign monitoring
2. **Commission Notifications** - Immediate feedback on earnings
3. **System Alerts** - Critical updates and maintenance notices
4. **Team Collaboration** - Shared workspace updates

### **Optional Use Cases:**
1. **Click-by-click Tracking** - May be overwhelming for high-traffic users
2. **Real-time Analytics** - Can supplement but not replace traditional analytics
3. **Chat/Messaging** - If you add team communication features

### **Performance Considerations:**
- WebSocket connections consume server resources
- Consider connection limits per user
- Implement proper cleanup and reconnection logic
- Use Redis for scaling across multiple server instances

The real-time features enhance user experience significantly, especially for active affiliate marketers who want immediate feedback on their campaigns' performance.
