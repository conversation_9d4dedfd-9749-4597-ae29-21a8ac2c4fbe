# Backend Database Setup Guide
## FastAPI + PostgreSQL Implementation

This guide provides scripts and instructions for setting up the PostgreSQL database schema for the Affiliate Middleware Platform.

## 📋 Prerequisites

- PostgreSQL 14+ installed and running
- Python 3.9+ with pip
- psycopg2 or asyncpg for database connectivity

## 🗄️ Database Schema Creation Script

### 1. Create Database Setup Script

Create `scripts/create_database_schema.sql`:

```sql
-- ============================================================================
-- AFFILIATE MIDDLEWARE PLATFORM - DATABASE SCHEMA
-- ============================================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- ENUMS
-- ============================================================================

-- User types for affiliate platform
CREATE TYPE user_type_enum AS ENUM ('individual', 'agency', 'developer');

-- KYC verification status
CREATE TYPE kyc_status_enum AS ENUM ('pending', 'verified', 'rejected', 'not_required');

-- Payout methods
CREATE TYPE payout_method_enum AS ENUM ('bank_transfer', 'paypal', 'stripe', 'wise');

-- Authentication types for affiliate networks
CREATE TYPE auth_type_enum AS ENUM ('oauth2', 'api_key', 'basic_auth', 'bearer_token');

-- Commission types
CREATE TYPE commission_type_enum AS ENUM ('percentage', 'fixed', 'tiered', 'hybrid');

-- Program and enrollment statuses
CREATE TYPE program_status_enum AS ENUM ('active', 'paused', 'expired', 'pending_approval', 'rejected');
CREATE TYPE enrollment_status_enum AS ENUM ('pending', 'approved', 'rejected', 'suspended', 'cancelled');

-- Link and tracking statuses
CREATE TYPE link_type_enum AS ENUM ('direct', 'iframe', 'widget', 'api', 'social');
CREATE TYPE click_status_enum AS ENUM ('pending', 'confirmed', 'rejected', 'fraud');

-- Commission and payout statuses
CREATE TYPE commission_status_enum AS ENUM ('pending', 'confirmed', 'paid', 'rejected', 'disputed');
CREATE TYPE payout_status_enum AS ENUM ('pending', 'processing', 'completed', 'failed', 'cancelled');

-- Campaign status
CREATE TYPE campaign_status_enum AS ENUM ('draft', 'active', 'paused', 'completed', 'archived');

-- ============================================================================
-- ENHANCED EXISTING TABLES
-- ============================================================================

-- Enhance users table with affiliate-specific fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS user_type user_type_enum DEFAULT 'individual';
ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_status kyc_status_enum DEFAULT 'not_required';
ALTER TABLE users ADD COLUMN IF NOT EXISTS affiliate_id VARCHAR(50) UNIQUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS tax_info JSONB DEFAULT '{}';
ALTER TABLE users ADD COLUMN IF NOT EXISTS payout_method payout_method_enum DEFAULT 'stripe';
ALTER TABLE users ADD COLUMN IF NOT EXISTS payout_details JSONB DEFAULT '{}';
ALTER TABLE users ADD COLUMN IF NOT EXISTS total_earnings DECIMAL(12,2) DEFAULT 0.00;
ALTER TABLE users ADD COLUMN IF NOT EXISTS pending_earnings DECIMAL(12,2) DEFAULT 0.00;
ALTER TABLE users ADD COLUMN IF NOT EXISTS lifetime_clicks INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS lifetime_conversions INTEGER DEFAULT 0;

-- Enhance subscriptions table with affiliate quotas
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS max_affiliate_programs INTEGER DEFAULT 10;
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS max_tracking_links INTEGER DEFAULT 100;
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS max_api_calls INTEGER DEFAULT 1000;
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS advanced_analytics BOOLEAN DEFAULT false;
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS white_label_access BOOLEAN DEFAULT false;
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS priority_support BOOLEAN DEFAULT false;

-- ============================================================================
-- NEW AFFILIATE-SPECIFIC TABLES
-- ============================================================================

-- Affiliate Networks Table
CREATE TABLE affiliate_networks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    api_endpoint VARCHAR(255),
    auth_type auth_type_enum NOT NULL,
    auth_config JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    supported_features JSONB DEFAULT '{}',
    rate_limit INTEGER DEFAULT 1000,
    logo_url VARCHAR(255),
    website_url VARCHAR(255),
    documentation_url VARCHAR(255),
    support_email VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Affiliate Programs Table
CREATE TABLE affiliate_programs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    network_id UUID NOT NULL REFERENCES affiliate_networks(id) ON DELETE CASCADE,
    external_id VARCHAR(100) NOT NULL,
    name VARCHAR(200) NOT NULL,
    brand VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    commission_type commission_type_enum NOT NULL,
    commission_rate DECIMAL(5,2),
    commission_structure JSONB DEFAULT '{}',
    cookie_duration INTEGER DEFAULT 30,
    payout_terms TEXT,
    restrictions JSONB DEFAULT '{}',
    status program_status_enum DEFAULT 'active',
    min_payout DECIMAL(10,2) DEFAULT 0.00,
    avg_earnings_per_click DECIMAL(6,4) DEFAULT 0.00,
    conversion_rate DECIMAL(5,4) DEFAULT 0.00,
    logo_url VARCHAR(255),
    website_url VARCHAR(255),
    terms_url VARCHAR(255),
    application_url VARCHAR(255),
    tags JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(network_id, external_id)
);

-- User Program Enrollments Table
CREATE TABLE user_program_enrollments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    program_id UUID NOT NULL REFERENCES affiliate_programs(id) ON DELETE CASCADE,
    workspace_id UUID REFERENCES workspaces(id) ON DELETE SET NULL,
    status enrollment_status_enum DEFAULT 'pending',
    application_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    approval_date TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    custom_commission_rate DECIMAL(5,2),
    tracking_id VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, program_id)
);

-- Campaigns Table
CREATE TABLE campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    workspace_id UUID REFERENCES workspaces(id) ON DELETE SET NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP WITH TIME ZONE,
    budget DECIMAL(10,2),
    target_clicks INTEGER,
    target_conversions INTEGER,
    status campaign_status_enum DEFAULT 'draft',
    tags JSONB DEFAULT '[]',
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tracking Links Table
CREATE TABLE tracking_links (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    program_id UUID NOT NULL REFERENCES affiliate_programs(id) ON DELETE CASCADE,
    workspace_id UUID REFERENCES workspaces(id) ON DELETE SET NULL,
    campaign_id UUID REFERENCES campaigns(id) ON DELETE SET NULL,
    original_url TEXT NOT NULL,
    tracking_url TEXT UNIQUE NOT NULL,
    short_code VARCHAR(20) UNIQUE NOT NULL,
    link_type link_type_enum DEFAULT 'direct',
    custom_parameters JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE,
    click_count INTEGER DEFAULT 0,
    conversion_count INTEGER DEFAULT 0,
    total_commission DECIMAL(10,2) DEFAULT 0.00,
    last_click_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Click Events Table
CREATE TABLE click_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tracking_link_id UUID NOT NULL REFERENCES tracking_links(id) ON DELETE CASCADE,
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    country VARCHAR(2),
    region VARCHAR(100),
    city VARCHAR(100),
    device VARCHAR(50),
    browser VARCHAR(50),
    os VARCHAR(50),
    clicked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    conversion_at TIMESTAMP WITH TIME ZONE,
    commission_amount DECIMAL(10,2),
    conversion_value DECIMAL(10,2),
    status click_status_enum DEFAULT 'pending',
    external_transaction_id VARCHAR(100),
    session_id VARCHAR(100),
    fingerprint VARCHAR(100)
);

-- Commissions Table
CREATE TABLE commissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    program_id UUID NOT NULL REFERENCES affiliate_programs(id) ON DELETE CASCADE,
    tracking_link_id UUID REFERENCES tracking_links(id) ON DELETE SET NULL,
    click_event_id UUID REFERENCES click_events(id) ON DELETE SET NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    commission_type VARCHAR(50) DEFAULT 'conversion',
    status commission_status_enum DEFAULT 'pending',
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    paid_at TIMESTAMP WITH TIME ZONE,
    payout_id UUID,
    external_transaction_id VARCHAR(100),
    order_value DECIMAL(10,2),
    commission_rate DECIMAL(5,2),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Payouts Table
CREATE TABLE payouts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    method payout_method_enum NOT NULL,
    status payout_status_enum DEFAULT 'pending',
    scheduled_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE,
    external_transaction_id VARCHAR(100),
    fees DECIMAL(10,2) DEFAULT 0.00,
    net_amount DECIMAL(10,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- API Keys Table
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    workspace_id UUID REFERENCES workspaces(id) ON DELETE SET NULL,
    name VARCHAR(100) NOT NULL,
    key_hash VARCHAR(255) NOT NULL UNIQUE,
    permissions JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    rate_limit INTEGER DEFAULT 1000,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Webhook Endpoints Table
CREATE TABLE webhook_endpoints (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    workspace_id UUID REFERENCES workspaces(id) ON DELETE SET NULL,
    url VARCHAR(255) NOT NULL,
    events JSONB DEFAULT '[]',
    secret VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_triggered_at TIMESTAMP WITH TIME ZONE,
    failure_count INTEGER DEFAULT 0,
    max_failures INTEGER DEFAULT 5,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Affiliate Networks indexes
CREATE INDEX idx_affiliate_networks_slug ON affiliate_networks(slug);
CREATE INDEX idx_affiliate_networks_active ON affiliate_networks(is_active);

-- Affiliate Programs indexes
CREATE INDEX idx_affiliate_programs_network ON affiliate_programs(network_id);
CREATE INDEX idx_affiliate_programs_status ON affiliate_programs(status);
CREATE INDEX idx_affiliate_programs_category ON affiliate_programs(category);
CREATE INDEX idx_affiliate_programs_brand ON affiliate_programs(brand);

-- User Program Enrollments indexes
CREATE INDEX idx_user_enrollments_user ON user_program_enrollments(user_id);
CREATE INDEX idx_user_enrollments_program ON user_program_enrollments(program_id);
CREATE INDEX idx_user_enrollments_status ON user_program_enrollments(status);

-- Tracking Links indexes
CREATE INDEX idx_tracking_links_user ON tracking_links(user_id);
CREATE INDEX idx_tracking_links_program ON tracking_links(program_id);
CREATE INDEX idx_tracking_links_campaign ON tracking_links(campaign_id);
CREATE INDEX idx_tracking_links_short_code ON tracking_links(short_code);
CREATE INDEX idx_tracking_links_active ON tracking_links(is_active);

-- Click Events indexes
CREATE INDEX idx_click_events_tracking_link ON click_events(tracking_link_id);
CREATE INDEX idx_click_events_clicked_at ON click_events(clicked_at);
CREATE INDEX idx_click_events_status ON click_events(status);
CREATE INDEX idx_click_events_country ON click_events(country);

-- Commissions indexes
CREATE INDEX idx_commissions_user ON commissions(user_id);
CREATE INDEX idx_commissions_program ON commissions(program_id);
CREATE INDEX idx_commissions_status ON commissions(status);
CREATE INDEX idx_commissions_transaction_date ON commissions(transaction_date);
CREATE INDEX idx_commissions_payout ON commissions(payout_id);

-- Payouts indexes
CREATE INDEX idx_payouts_user ON payouts(user_id);
CREATE INDEX idx_payouts_status ON payouts(status);
CREATE INDEX idx_payouts_scheduled_at ON payouts(scheduled_at);

-- API Keys indexes
CREATE INDEX idx_api_keys_user ON api_keys(user_id);
CREATE INDEX idx_api_keys_hash ON api_keys(key_hash);
CREATE INDEX idx_api_keys_active ON api_keys(is_active);

-- ============================================================================
-- TRIGGERS FOR UPDATED_AT
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables with updated_at
CREATE TRIGGER update_affiliate_networks_updated_at BEFORE UPDATE ON affiliate_networks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_affiliate_programs_updated_at BEFORE UPDATE ON affiliate_programs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_program_enrollments_updated_at BEFORE UPDATE ON user_program_enrollments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_campaigns_updated_at BEFORE UPDATE ON campaigns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tracking_links_updated_at BEFORE UPDATE ON tracking_links FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_commissions_updated_at BEFORE UPDATE ON commissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payouts_updated_at BEFORE UPDATE ON payouts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_webhook_endpoints_updated_at BEFORE UPDATE ON webhook_endpoints FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 🚀 Execution Instructions

### 1. Run the Schema Creation Script

```bash
# Connect to PostgreSQL and run the schema
psql -U your_username -d your_database -f scripts/create_database_schema.sql

# Or using environment variables
PGPASSWORD=your_password psql -h localhost -U your_username -d your_database -f scripts/create_database_schema.sql
```

### 2. Verify Schema Creation

```sql
-- Check if all tables were created
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check if all enums were created
SELECT typname FROM pg_type WHERE typtype = 'e' ORDER BY typname;
```

## 🔧 Python Database Connection Setup

Create `database/connection.py`:

```python
import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool

# Database URL from environment
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://user:password@localhost/affiliate_db")

# Create engine
engine = create_engine(
    DATABASE_URL,
    poolclass=NullPool,  # For development
    echo=True  # Set to False in production
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# Dependency for FastAPI
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

## 📝 Next Steps

1. **Run the schema creation script** to set up all tables and relationships
2. **Execute the test data script** (see BACKEND_TEST_DATA.md)
3. **Implement FastAPI models** using SQLAlchemy
4. **Set up API endpoints** (see BACKEND_API_SPECIFICATION.md)
5. **Configure external integrations** (see BACKEND_EXTERNAL_API_INTEGRATIONS.md)
6. **Set up background jobs** (see BACKEND_BACKGROUND_JOBS.md)
7. **Deploy with Docker** (see BACKEND_DOCKER_DEPLOYMENT.md)

## 🔗 Related Documentation

### **Core Setup**
- `BACKEND_TEST_DATA.md` - Populate database with realistic test data
- `BACKEND_API_SPECIFICATION.md` - Complete API implementation guide

### **Advanced Features**
- `BACKEND_EXTERNAL_API_INTEGRATIONS.md` - Affiliate network API integrations
- `BACKEND_REALTIME_FEATURES.md` - WebSocket real-time updates
- `BACKEND_BACKGROUND_JOBS.md` - Celery task processing setup
- `BACKEND_DOCKER_DEPLOYMENT.md` - Complete containerization

### **Architecture Overview**
- `COMPREHENSIVE_ENTITIES_DOCUMENTATION.md` - Complete system documentation
- `DATABASE_SCHEMA_SUMMARY.md` - Existing schema overview

## 🚀 Quick Start with Docker

For the fastest setup, use the Docker configuration:

```bash
# Clone and setup
git clone <repository>
cd affiliate-platform

# Copy environment variables
cp .env.example .env

# Start development environment
docker-compose up -d

# Run database migrations
docker-compose exec web alembic upgrade head

# Populate test data
docker-compose exec web python scripts/populate_test_data.py

# Access the application
open http://localhost:8000/docs
```

## 🔍 Troubleshooting

### Common Issues:
- **Permission denied**: Ensure your PostgreSQL user has CREATE privileges
- **Extension not found**: Install uuid-ossp extension: `CREATE EXTENSION "uuid-ossp";`
- **Foreign key errors**: Ensure existing tables (users, workspaces, etc.) exist first
- **Docker issues**: Check `BACKEND_DOCKER_DEPLOYMENT.md` for container troubleshooting
- **Integration errors**: Verify API credentials in `BACKEND_EXTERNAL_API_INTEGRATIONS.md`

### Performance Optimization:
- **Indexes**: All critical indexes are included in the schema
- **Partitioning**: Consider partitioning large tables (click_events, commissions) by date
- **Connection pooling**: Configure appropriate connection pool sizes
- **Caching**: Redis caching is configured for frequently accessed data
