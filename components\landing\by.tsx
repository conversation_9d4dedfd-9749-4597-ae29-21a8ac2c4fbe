import Image from "next/image"
import Link from "next/link"

import { Icons } from "@/components/global/icons"

export function By() {
  return (
    <section className="border-y">
      <div className="mx-auto max-w-7xl px-6 py-24">
        <div className="flex flex-col items-center justify-center gap-6 text-center">
          <p className="text-sm font-medium text-orange-500">Meet the Creator</p>

          <div className="relative">
            <Image
              src="/kevin2.jpg"
              width={400}
              height={400}
              alt="<PERSON>"
              className="size-24 rounded-full object-cover select-none"
              priority
            />

            <svg
              className="absolute -top-5 -right-4 size-10 rotate-45 md:size-12"
              viewBox="0 0 154 136"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_8_2867)">
                <path
                  className="fill-black dark:fill-white"
                  d="M127.902 44.3342C125.623 44.3703 124.281 43.7653 123.466 42.1855C122.69 40.6838 123.198 39.3362 124.022 38.0938C124.455 37.4383 125.018 36.8796 125.678 36.4529C129.18 34.2536 132.67 32.0219 136.285 30.0208C137.482 29.4368 138.822 29.2089 140.145 29.3644C140.758 29.4343 141.346 29.6482 141.86 29.9886C142.375 30.329 142.801 30.7863 143.106 31.3231C143.41 31.8598 143.582 32.4609 143.61 33.0772C143.637 33.6935 143.519 34.3075 143.263 34.8691C142.619 36.2881 141.598 37.503 140.31 38.3813C137.073 40.345 133.647 42.0049 130.25 43.6937C129.497 44.0034 128.708 44.2187 127.902 44.3342Z"
                ></path>
                <path
                  className="fill-black dark:fill-white"
                  d="M21.6685 49.0745C20.9465 48.6676 19.311 48.0954 18.1598 47.0071C16.3063 45.218 14.6241 43.2597 13.1348 41.1578C12.3408 40.0882 11.8856 38.8053 11.828 37.4746C11.8549 35.0461 14.4127 33.2943 17.0006 34.16C19.1481 34.8232 21.1433 35.9043 22.8714 37.3414C24.5996 38.7784 26.0265 40.5431 27.0702 42.5336C28.8154 45.9413 26.4105 49.1848 21.6685 49.0745Z"
                ></path>
                <path
                  className="fill-black dark:fill-white"
                  d="M114.501 0.366583C117.943 0.34558 120.014 2.92035 119.334 5.62578C119.145 6.50263 118.742 7.31858 118.158 7.99973C115.827 10.4072 113.475 12.8134 110.939 14.997C109.707 16.0563 108.152 16.8478 106.282 16.2177C104.105 15.484 103.055 13.6935 103.761 11.5256C103.943 10.8763 104.25 10.2683 104.663 9.73509C106.852 7.19703 109.013 4.62155 111.374 2.24955C112.345 1.27423 113.795 0.776793 114.501 0.366583Z"
                ></path>
                <path
                  className="fill-black dark:fill-white"
                  d="M53.3557 13.062C53.1588 15.0507 52.3273 16.5229 50.5335 17.3886C48.8559 18.1972 47.404 17.6419 46.2384 16.488C43.7066 14.0883 41.8036 11.1029 40.697 7.79478C39.9586 5.45625 40.653 3.50178 42.347 2.45098C44.1486 1.33521 46.1072 1.49196 47.8182 3.3238C50.4285 6.12242 52.618 9.22763 53.3557 13.062Z"
                ></path>
                <path
                  className="fill-black dark:fill-white"
                  d="M152.875 64.8372C152.824 63.1477 152.525 61.4751 151.985 59.8733C151.203 57.7487 149.793 56.3054 148.026 55.8204C146.239 55.3275 144.269 55.8604 142.482 57.3103C141.133 58.4031 139.812 59.597 138.537 60.7501C138.012 61.222 137.487 61.7011 136.962 62.1664L129.961 68.391C124.248 73.4697 118.532 78.5484 112.81 83.6265C112.4 83.9698 111.968 84.2855 111.516 84.5703C111.333 84.6918 111.145 84.8132 110.956 84.9484C110.828 85.0357 110.682 85.0928 110.529 85.1151C110.376 85.1381 110.22 85.1263 110.072 85.0797C109.923 85.0337 109.788 84.9536 109.675 84.846C109.563 84.7384 109.477 84.6058 109.424 84.4594C108.15 80.9723 106.909 77.5022 105.668 74.046C102.912 66.3668 100.057 58.4201 97.0702 50.7318C92.9083 40.0335 88.4439 29.6554 84.2493 20.0315C83.1545 17.7057 81.6672 15.5862 79.8518 13.7662C78.2195 12.0597 76.4323 11.9317 74.0649 13.3684C72.1365 14.5282 71.3135 16.5379 70.4432 18.6625L70.3625 18.8594C69.4928 20.9636 68.6324 23.0723 67.7798 25.1857C65.1695 31.5929 62.4719 38.2291 59.6057 44.6632C55.2738 54.3947 50.5883 64.1676 46.1573 73.345C44.6536 76.4606 43.0148 79.4548 41.2814 82.625C40.4938 84.0689 39.6898 85.5384 38.8871 87.0493C38.8223 87.1727 38.7329 87.281 38.6244 87.3677C38.5158 87.4543 38.3905 87.518 38.2563 87.5541C38.1221 87.5902 37.9818 87.598 37.8444 87.5777C37.707 87.5567 37.5752 87.5081 37.4576 87.4339L34.611 85.6461C32.8035 84.5198 31.158 83.4953 29.5192 82.4563C27.2128 80.9999 24.9062 79.5408 22.5994 78.0792C18.0812 75.2129 13.5581 72.3579 9.03026 69.514C8.01503 68.771 6.81172 68.3266 5.55691 68.2328C4.63274 68.2623 3.75076 68.6266 3.0753 69.258C1.08528 70.8896 0.525441 72.9066 1.36883 75.4276C1.90834 77.0526 2.63688 78.6718 3.33785 80.2431L3.37198 80.3087C3.9384 81.584 4.55862 82.8717 5.15195 84.1194C6.27166 86.4731 7.43864 88.9107 8.37589 91.4048C8.97618 92.9826 9.57876 94.5631 10.1834 96.1462C14.048 106.269 18.0405 116.736 20.6507 127.513C21.5342 131.169 23.6449 133.145 27.1045 133.556C28.0555 133.671 29.0079 133.792 29.951 133.914C33.8317 134.498 37.7484 134.812 41.6726 134.851C50.5883 134.737 59.6523 134.406 68.4204 134.089C72.2777 133.947 76.133 133.813 79.9864 133.685C86.3128 133.482 92.6392 133.288 98.965 133.105L106.229 132.882C106.815 132.868 107.402 132.848 107.996 132.835C108.636 132.821 109.284 132.801 109.931 132.781C112.177 132.715 114.423 132.625 116.668 132.545C122.182 132.343 127.871 132.133 133.482 132.1C136.072 132.086 137.9 131.181 139.242 129.26C140.167 127.85 140.87 126.306 141.326 124.681L142.054 122.293C145.028 112.555 148.097 102.479 149.904 92.2817C150.08 91.2899 150.262 90.3054 150.437 89.3137C151.877 81.3471 153.374 73.0916 152.875 64.8372ZM130.396 120.403C130.332 120.581 130.219 120.737 130.07 120.853C129.921 120.969 129.741 121.04 129.554 121.059C128.744 121.147 127.908 121.248 127.065 121.35C125.136 121.586 123.133 121.829 121.11 121.916C117.791 122.058 114.42 122.159 111.162 122.244C106.819 122.375 102.327 122.5 97.9228 122.743C92.7908 123.033 87.5703 123.458 82.5257 123.869C79.626 124.106 76.7257 124.342 73.826 124.55C69.5906 124.861 66.0562 125.11 62.3741 125.225C53.3029 125.515 44.0833 125.75 35.1676 125.981L30.7504 126.095H30.6487C30.2384 126.086 29.8299 126.038 29.4286 125.953C29.2934 125.926 29.1588 125.899 29.0302 125.887C28.8387 125.861 28.6589 125.78 28.5119 125.655C28.3649 125.529 28.2569 125.364 28.2006 125.179L16.4863 87.0743C16.424 86.878 16.4238 86.6667 16.4855 86.4698C16.5472 86.2729 16.668 86.1003 16.8314 85.9743C16.9947 85.8482 17.1929 85.7754 17.399 85.7662C17.605 85.7563 17.809 85.8102 17.9834 85.9204L19.5008 86.8583C20.5057 87.4786 21.4298 88.045 22.3401 88.6252C23.6348 89.4482 24.9298 90.2667 26.225 91.0799C29.4351 93.1099 32.7602 95.201 35.9637 97.3722C38.0135 98.7617 39.8079 99.2946 41.2847 98.9573C42.7286 98.6291 44.0026 97.4871 45.1695 95.4708L46.2197 93.6698C48.2025 90.2706 50.2529 86.75 51.9587 83.1684C57.6032 71.2716 63.2621 59.1323 68.7321 47.3904C70.8902 42.757 73.0528 38.1237 75.22 33.4905C75.4018 33.0966 75.5909 32.7145 75.8606 32.1822L76.3798 31.1367C76.4631 30.9612 76.597 30.8144 76.7637 30.7148C76.9311 30.6152 77.1234 30.5673 77.3177 30.577C77.5113 30.5817 77.6997 30.642 77.8598 30.7508C78.02 30.8597 78.1453 31.0123 78.2208 31.1905C79.0169 33.0657 79.8196 34.8798 80.6086 36.6604C82.3216 40.5328 83.9401 44.1868 85.323 47.9837C88.4859 56.6907 91.5812 65.5735 94.5826 74.1713C96.3022 79.0952 98.0291 84.021 99.7625 88.9481C100.619 91.4606 101.619 93.9218 102.757 96.3194C103.131 97.1943 103.705 97.9688 104.434 98.5805C105.163 99.1922 106.025 99.6234 106.952 99.84C108.766 100.198 110.681 99.5703 112.354 98.0797C114.896 95.8141 117.426 93.5274 119.955 91.2479C122.174 89.2448 124.392 87.2462 126.611 85.2523C129.437 82.7306 132.272 80.2142 135.115 77.7044L138.922 74.3387C139.063 74.2146 139.234 74.1313 139.418 74.0985C139.603 74.065 139.793 74.0834 139.968 74.1503L140.669 74.4266C140.883 74.5106 141.064 74.6649 141.179 74.8651C141.294 75.0653 141.338 75.2983 141.303 75.526C139.231 88.7453 136.318 104.419 130.396 120.403Z"
                ></path>
              </g>
            </svg>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Kevin Nguyen</h3>
            <p className="text-muted-foreground mx-auto max-w-lg text-sm md:text-base">
              Full-time software developer with a passion for building SaaS products. After spending
              countless hours building startups from scratch, I created this template to help other
              creators launch faster and focus on what matters most - their unique product features.
            </p>
          </div>

          <div className="flex items-center gap-4">
            <Link
              href="https://twitter.com/kewinversi"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-primary/10 hover:bg-primary/20 rounded-full p-2 transition-colors"
            >
              <Icons.twitter className="size-5" />
            </Link>
            <Link
              href="https://www.youtube.com/@kevinnguuyen"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-primary/10 hover:bg-primary/20 rounded-full p-2 transition-colors"
            >
              <Icons.youtube className="size-5" />
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
