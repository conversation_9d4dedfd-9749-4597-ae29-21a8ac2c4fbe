#!/usr/bin/env python3
"""
Entity Relationships PDF Generator
Generates a comprehensive PDF document showing all entity relationships
in the affiliate middleware platform.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
import io
import base64
from datetime import datetime

def create_entity_diagram():
    """Create a visual entity relationship diagram"""
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # Define colors for different entity types
    colors_dict = {
        'core': '#E3F2FD',      # Light Blue - Core entities
        'affiliate': '#E8F5E8',  # Light Green - Affiliate entities
        'tracking': '#FFF3E0',   # Light Orange - Tracking entities
        'financial': '#F3E5F5',  # Light Purple - Financial entities
        'integration': '#FFEBEE' # Light Red - Integration entities
    }
    
    # Entity definitions with positions and types
    entities = {
        # Core entities (top row)
        'Users': {'pos': (2, 10), 'type': 'core', 'size': (1.8, 0.8)},
        'User Settings': {'pos': (0.5, 8.5), 'type': 'core', 'size': (1.5, 0.6)},
        'Subscriptions': {'pos': (3.5, 8.5), 'type': 'core', 'size': (1.5, 0.6)},
        'Workspaces': {'pos': (5.5, 10), 'type': 'core', 'size': (1.5, 0.8)},
        'Notifications': {'pos': (7.5, 8.5), 'type': 'core', 'size': (1.5, 0.6)},
        
        # Affiliate entities (middle-left)
        'Affiliate Networks': {'pos': (1, 6.5), 'type': 'affiliate', 'size': (1.8, 0.8)},
        'Affiliate Programs': {'pos': (4, 6.5), 'type': 'affiliate', 'size': (1.8, 0.8)},
        'User Program Enrollments': {'pos': (7, 6.5), 'type': 'affiliate', 'size': (2.2, 0.8)},
        
        # Campaign and tracking entities (middle)
        'Campaigns': {'pos': (10.5, 8), 'type': 'tracking', 'size': (1.5, 0.8)},
        'Tracking Links': {'pos': (10.5, 6), 'type': 'tracking', 'size': (1.8, 0.8)},
        'Click Events': {'pos': (13.5, 6), 'type': 'tracking', 'size': (1.5, 0.8)},
        
        # Financial entities (bottom)
        'Commissions': {'pos': (11, 4), 'type': 'financial', 'size': (1.8, 0.8)},
        'Payouts': {'pos': (14, 4), 'type': 'financial', 'size': (1.5, 0.8)},
        
        # Integration entities (bottom-left)
        'API Keys': {'pos': (2, 2.5), 'type': 'integration', 'size': (1.5, 0.8)},
        'Webhook Endpoints': {'pos': (5, 2.5), 'type': 'integration', 'size': (2, 0.8)},
    }
    
    # Draw entities
    entity_centers = {}
    for name, info in entities.items():
        x, y = info['pos']
        w, h = info['size']
        color = colors_dict[info['type']]
        
        # Create rounded rectangle
        rect = FancyBboxPatch(
            (x - w/2, y - h/2), w, h,
            boxstyle="round,pad=0.05",
            facecolor=color,
            edgecolor='black',
            linewidth=1.5
        )
        ax.add_patch(rect)
        
        # Add text
        ax.text(x, y, name, ha='center', va='center', fontsize=9, fontweight='bold')
        entity_centers[name] = (x, y)
    
    # Define relationships with labels
    relationships = [
        # Core relationships
        ('Users', 'User Settings', '1:1'),
        ('Users', 'Subscriptions', '1:1'),
        ('Users', 'Workspaces', '1:M'),
        ('Users', 'Notifications', '1:M'),
        
        # Affiliate relationships
        ('Affiliate Networks', 'Affiliate Programs', '1:M'),
        ('Users', 'User Program Enrollments', '1:M'),
        ('Affiliate Programs', 'User Program Enrollments', '1:M'),
        
        # Tracking relationships
        ('Users', 'Campaigns', '1:M'),
        ('Campaigns', 'Tracking Links', '1:M'),
        ('Affiliate Programs', 'Tracking Links', '1:M'),
        ('Tracking Links', 'Click Events', '1:M'),
        
        # Financial relationships
        ('Click Events', 'Commissions', '1:1'),
        ('Users', 'Commissions', '1:M'),
        ('Affiliate Programs', 'Commissions', '1:M'),
        ('Payouts', 'Commissions', '1:M'),
        ('Users', 'Payouts', '1:M'),
        
        # Integration relationships
        ('Users', 'API Keys', '1:M'),
        ('Users', 'Webhook Endpoints', '1:M'),
        ('Workspaces', 'API Keys', '1:M'),
    ]
    
    # Draw relationships
    for entity1, entity2, rel_type in relationships:
        if entity1 in entity_centers and entity2 in entity_centers:
            x1, y1 = entity_centers[entity1]
            x2, y2 = entity_centers[entity2]
            
            # Draw arrow
            ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                       arrowprops=dict(arrowstyle='->', color='gray', lw=1.5))
            
            # Add relationship label
            mid_x, mid_y = (x1 + x2) / 2, (y1 + y2) / 2
            ax.text(mid_x, mid_y, rel_type, ha='center', va='center',
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8),
                   fontsize=7, fontweight='bold')
    
    # Add title
    ax.text(8, 11.5, 'Affiliate Middleware Platform - Entity Relationships',
           ha='center', va='center', fontsize=16, fontweight='bold')
    
    # Add legend
    legend_y = 1.5
    for i, (entity_type, color) in enumerate(colors_dict.items()):
        x = 1 + i * 2.5
        rect = FancyBboxPatch(
            (x - 0.3, legend_y - 0.15), 0.6, 0.3,
            boxstyle="round,pad=0.02",
            facecolor=color,
            edgecolor='black'
        )
        ax.add_patch(rect)
        ax.text(x, legend_y, entity_type.title(), ha='center', va='center', fontsize=8)
    
    plt.tight_layout()
    return fig

def create_relationship_matrix():
    """Create a relationship matrix visualization"""
    entities = [
        'Users', 'User Settings', 'Subscriptions', 'Workspaces', 'Notifications',
        'Affiliate Networks', 'Affiliate Programs', 'User Program Enrollments',
        'Campaigns', 'Tracking Links', 'Click Events', 'Commissions', 'Payouts',
        'API Keys', 'Webhook Endpoints'
    ]
    
    # Create relationship matrix
    matrix = np.zeros((len(entities), len(entities)))
    relationships = {
        ('Users', 'User Settings'): 1,  # 1:1
        ('Users', 'Subscriptions'): 1,  # 1:1
        ('Users', 'Workspaces'): 2,     # 1:M
        ('Users', 'Notifications'): 2,  # 1:M
        ('Affiliate Networks', 'Affiliate Programs'): 2,  # 1:M
        ('Users', 'User Program Enrollments'): 2,  # 1:M
        ('Affiliate Programs', 'User Program Enrollments'): 2,  # 1:M
        ('Users', 'Campaigns'): 2,      # 1:M
        ('Campaigns', 'Tracking Links'): 2,  # 1:M
        ('Affiliate Programs', 'Tracking Links'): 2,  # 1:M
        ('Tracking Links', 'Click Events'): 2,  # 1:M
        ('Click Events', 'Commissions'): 1,  # 1:1
        ('Users', 'Commissions'): 2,    # 1:M
        ('Affiliate Programs', 'Commissions'): 2,  # 1:M
        ('Payouts', 'Commissions'): 2,  # 1:M
        ('Users', 'Payouts'): 2,        # 1:M
        ('Users', 'API Keys'): 2,       # 1:M
        ('Users', 'Webhook Endpoints'): 2,  # 1:M
        ('Workspaces', 'API Keys'): 2,  # 1:M
    }
    
    entity_to_idx = {entity: i for i, entity in enumerate(entities)}
    
    for (entity1, entity2), rel_type in relationships.items():
        if entity1 in entity_to_idx and entity2 in entity_to_idx:
            i, j = entity_to_idx[entity1], entity_to_idx[entity2]
            matrix[i][j] = rel_type
            matrix[j][i] = rel_type  # Make symmetric
    
    fig, ax = plt.subplots(figsize=(12, 10))
    im = ax.imshow(matrix, cmap='RdYlBu_r', aspect='equal')
    
    # Set ticks and labels
    ax.set_xticks(range(len(entities)))
    ax.set_yticks(range(len(entities)))
    ax.set_xticklabels(entities, rotation=45, ha='right')
    ax.set_yticklabels(entities)
    
    # Add relationship type annotations
    for i in range(len(entities)):
        for j in range(len(entities)):
            if matrix[i][j] == 1:
                text = '1:1'
            elif matrix[i][j] == 2:
                text = '1:M'
            else:
                text = ''
            
            if text:
                ax.text(j, i, text, ha='center', va='center', fontweight='bold')
    
    ax.set_title('Entity Relationship Matrix\n(1:1 = One-to-One, 1:M = One-to-Many)', 
                fontsize=14, fontweight='bold', pad=20)
    
    plt.tight_layout()
    return fig

def generate_pdf():
    """Generate the complete PDF document"""
    filename = f"Entity_Relationships_Summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
    doc = SimpleDocTemplate(filename, pagesize=A4)
    
    # Get styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=TA_CENTER
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
        spaceBefore=20
    )
    
    # Build document content
    story = []
    
    # Title page
    story.append(Paragraph("Affiliate Middleware Platform", title_style))
    story.append(Paragraph("Entity Relationships Summary", title_style))
    story.append(Spacer(1, 0.5*inch))
    story.append(Paragraph(f"Generated on: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}", styles['Normal']))
    story.append(PageBreak())
    
    # Executive Summary
    story.append(Paragraph("Executive Summary", heading_style))
    summary_text = """
    This document provides a comprehensive overview of the entity relationships in the 
    Affiliate Middleware Platform. The platform consists of 15 core entities organized 
    into 5 categories: Core Foundation, Affiliate-Specific, Tracking & Analytics, 
    Financial, and Integration entities.
    
    The database design supports complex affiliate marketing workflows including user 
    management, program enrollment, campaign tracking, commission calculation, and 
    real-time integrations.
    """
    story.append(Paragraph(summary_text, styles['Normal']))
    story.append(Spacer(1, 0.3*inch))
    
    # Entity Categories
    story.append(Paragraph("Entity Categories", heading_style))
    
    categories_data = [
        ['Category', 'Entities', 'Purpose'],
        ['Core Foundation', 'Users, User Settings, Subscriptions, Workspaces, Notifications', 'Basic platform infrastructure'],
        ['Affiliate-Specific', 'Affiliate Networks, Affiliate Programs, User Program Enrollments', 'Affiliate program management'],
        ['Tracking & Analytics', 'Campaigns, Tracking Links, Click Events', 'Performance tracking'],
        ['Financial', 'Commissions, Payouts', 'Revenue management'],
        ['Integration', 'API Keys, Webhook Endpoints', 'External integrations']
    ]
    
    categories_table = Table(categories_data, colWidths=[1.5*inch, 3*inch, 2*inch])
    categories_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
    ]))
    
    story.append(categories_table)
    story.append(PageBreak())
    
    # Relationship Summary Table
    story.append(Paragraph("Relationship Summary", heading_style))
    
    relationships_data = [
        ['Entity 1', 'Relationship', 'Entity 2', 'Type', 'Purpose'],
        ['Users', '1:1', 'User Settings', 'One-to-One', 'User preferences'],
        ['Users', '1:1', 'Subscriptions', 'One-to-One', 'Billing management'],
        ['Users', '1:M', 'Workspaces', 'One-to-Many', 'Organization ownership'],
        ['Users', '1:M', 'Notifications', 'One-to-Many', 'Communication'],
        ['Affiliate Networks', '1:M', 'Affiliate Programs', 'One-to-Many', 'Program organization'],
        ['Users', 'M:M', 'Affiliate Programs', 'Many-to-Many', 'Program enrollment'],
        ['Users', '1:M', 'Campaigns', 'One-to-Many', 'Campaign management'],
        ['Campaigns', '1:M', 'Tracking Links', 'One-to-Many', 'Link organization'],
        ['Affiliate Programs', '1:M', 'Tracking Links', 'One-to-Many', 'Program-specific links'],
        ['Tracking Links', '1:M', 'Click Events', 'One-to-Many', 'Click tracking'],
        ['Click Events', '1:1', 'Commissions', 'One-to-One (Optional)', 'Conversion tracking'],
        ['Users', '1:M', 'Commissions', 'One-to-Many', 'Earnings tracking'],
        ['Payouts', '1:M', 'Commissions', 'One-to-Many', 'Payment batching'],
        ['Users', '1:M', 'API Keys', 'One-to-Many', 'API access'],
        ['Users', '1:M', 'Webhook Endpoints', 'One-to-Many', 'Integration hooks'],
    ]
    
    relationships_table = Table(relationships_data, colWidths=[1.2*inch, 0.8*inch, 1.2*inch, 1.2*inch, 1.6*inch])
    relationships_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 9),
        ('FONTSIZE', (0, 1), (-1, -1), 8),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
    ]))
    
    story.append(relationships_table)
    story.append(PageBreak())
    
    # Key Design Patterns
    story.append(Paragraph("Key Design Patterns", heading_style))
    
    patterns_text = """
    <b>1. Multi-tenancy via Workspaces:</b> Users can own multiple workspaces, enabling 
    agency and team use cases with proper data isolation.<br/><br/>
    
    <b>2. Flexible Program Enrollment:</b> Many-to-many relationship between users and 
    affiliate programs via junction table, tracking application status and custom rates.<br/><br/>
    
    <b>3. Hierarchical Campaign Organization:</b> Campaigns group related tracking links 
    with optional relationships allowing both organized and ad-hoc marketing.<br/><br/>
    
    <b>4. Complete Audit Trail:</b> All entities include timestamps, and click events 
    link directly to commissions for full tracking from click to payout.<br/><br/>
    
    <b>5. Extensible Integration System:</b> API keys support multiple permission levels 
    and webhooks enable real-time integrations with workspace-scoped access.
    """
    
    story.append(Paragraph(patterns_text, styles['Normal']))
    story.append(PageBreak())
    
    # Data Flow Examples
    story.append(Paragraph("Data Flow Examples", heading_style))
    
    flow_text = """
    <b>User Journey: From Click to Payout</b><br/>
    1. User creates Tracking Link for Affiliate Program<br/>
    2. Click Event is generated when link is clicked<br/>
    3. Commission is created when click converts<br/>
    4. Commission is included in Payout when threshold is met<br/><br/>
    
    <b>Program Discovery Flow</b><br/>
    1. Affiliate Network provides Affiliate Programs<br/>
    2. User applies via User Program Enrollment<br/>
    3. User creates Tracking Links for approved programs<br/>
    4. Click Events and Commissions track performance<br/><br/>
    
    <b>Team Collaboration Flow</b><br/>
    1. User creates Workspace for team<br/>
    2. API Keys are generated for workspace access<br/>
    3. Campaigns and Tracking Links are workspace-scoped<br/>
    4. Webhook Endpoints notify team systems of events
    """
    
    story.append(Paragraph(flow_text, styles['Normal']))
    
    # Build PDF
    doc.build(story)
    print(f"PDF generated successfully: {filename}")
    return filename

if __name__ == "__main__":
    # Generate diagrams
    print("Creating entity relationship diagram...")
    fig1 = create_entity_diagram()
    fig1.savefig('entity_relationships_diagram.png', dpi=300, bbox_inches='tight')
    plt.close(fig1)
    
    print("Creating relationship matrix...")
    fig2 = create_relationship_matrix()
    fig2.savefig('relationship_matrix.png', dpi=300, bbox_inches='tight')
    plt.close(fig2)
    
    # Generate PDF
    print("Generating PDF document...")
    filename = generate_pdf()
    
    print(f"\nGenerated files:")
    print(f"- {filename}")
    print(f"- entity_relationships_diagram.png")
    print(f"- relationship_matrix.png")
    print(f"- ENTITY_RELATIONSHIPS_SUMMARY.md")
