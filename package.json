{"name": "next-js-15-sass-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "bun server/db/seed.ts", "db:setup": "npm run db:migrate && npm run db:seed", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:drop": "drizzle-kit drop", "db:studio": "drizzle-kit studio", "check:unused": "depcheck", "check:files": "ts-prune", "format": "prettier --check --ignore-path .gitignore .", "format:fix": "prettier --write --ignore-path .gitignore ."}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@hookform/resolvers": "^3.10.0", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@react-email/components": "^0.0.23", "@tanstack/react-query": "^5.74.4", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.1.1", "@trpc/react-query": "^11.1.1", "@trpc/server": "^11.1.1", "@trpc/tanstack-react-query": "11.1.1", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.8", "@vercel/analytics": "^1.5.0", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.0.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.42.0", "drizzle-zod": "^0.5.1", "lucide-react": "^0.475.0", "motion": "^12.8.0", "next": "^15.3.1", "next-themes": "^0.4.6", "nuqs": "latest", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "recharts": "^2.15.3", "resend": "^4.4.0", "server-only": "^0.0.1", "sonner": "^1.7.4", "stripe": "^16.12.0", "superjson": "^2.2.2", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@tailwindcss/postcss": "^4.1.4", "@types/node": "^20.17.30", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/uuid": "^10.0.0", "@typescript-eslint/parser": "^8.31.0", "drizzle-kit": "^0.30.6", "eslint": "^8.57.1", "eslint-config-next": "15.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "postcss": "^8.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.4", "ts-prune": "^0.10.3", "typescript": "^5.8.3"}, "prettier": {"endOfLine": "lf", "semi": false, "singleQuote": false, "tabWidth": 2, "printWidth": 100, "trailingComma": "es5", "importOrder": ["^(react/(.*)$)|^(react$)", "^(next/(.*)$)|^(next$)", "<THIRD_PARTY_MODULES>", "", "^@workspace/(.*)$", "", "^types$", "^@/types/(.*)$", "^@/config/(.*)$", "^@/lib/(.*)$", "^@/hooks/(.*)$", "^@/components/ui/(.*)$", "^@/components/(.*)$", "^@/registry/(.*)$", "^@/styles/(.*)$", "^@/app/(.*)$", "^@/www/(.*)$", "", "^[./]"], "importOrderSeparation": false, "importOrderSortSpecifiers": true, "importOrderBuiltinModulesToTop": true, "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "importOrderMergeDuplicateImports": true, "importOrderCombineTypeAndValueImports": true, "plugins": ["@ianvs/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"]}, "overrides": {"@types/react": "19.0.10", "@types/react-dom": "19.0.4"}, "packageManager": "pnpm@10.8.1+sha512.c50088ba998c67b8ca8c99df8a5e02fd2ae2e2b29aaf238feaa9e124248d3f48f9fb6db2424949ff901cffbb5e0f0cc1ad6aedb602cd29450751d11c35023677"}