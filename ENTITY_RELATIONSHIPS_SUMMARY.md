# Entity Relationships Summary
## Affiliate Middleware Platform Database Design

This document provides a comprehensive summary of all entities and their relationships in the affiliate middleware platform.

## 📊 Entity Categories Overview

### **🏗️ Core Foundation Entities (Enhanced Existing)**
- **Users** - Core user accounts with affiliate enhancements
- **Workspaces** - Organizations/agencies managing campaigns
- **Subscriptions** - Billing plans with affiliate quotas
- **Notifications** - User notifications (enhanced)
- **User Settings** - User preferences (enhanced)

### **🔗 Affiliate-Specific Core Entities (New)**
- **Affiliate Networks** - External networks (AWIN, CJ, etc.)
- **Affiliate Programs** - Individual programs from networks
- **User Program Enrollments** - User-program relationships
- **Campaigns** - Campaign organization
- **Tracking Links** - Generated affiliate links

### **📊 Tracking & Analytics Entities (New)**
- **Click Events** - Individual click tracking
- **Commissions** - Commission tracking and earnings
- **Payouts** - Batch commission payments

### **🔧 Integration & API Entities (New)**
- **API Keys** - Developer API access
- **Webhook Endpoints** - Real-time notifications

## 🔗 Complete Entity Relationship Diagram

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           AFFILIATE MIDDLEWARE PLATFORM                          │
│                              ENTITY RELATIONSHIPS                               │
└─────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────┐    1:1     ┌─────────────────┐    1:1     ┌─────────────────┐
│   User Settings │◄──────────►│      Users      │◄──────────►│  Subscriptions  │
└─────────────────┘            │   (Enhanced)    │            │   (Enhanced)    │
                               └─────────┬───────┘            └─────────────────┘
                                         │ 1:M
                                         ▼
                               ┌─────────────────┐
                               │   Workspaces    │
                               │  (Repurposed)   │
                               └─────────┬───────┘
                                         │ 1:M
                                         ▼
┌─────────────────┐    M:M     ┌─────────────────┐    M:M     ┌─────────────────┐
│ Affiliate       │◄──────────►│ User Program    │◄──────────►│ Affiliate       │
│ Programs        │            │  Enrollments    │            │ Networks        │
└─────────┬───────┘            └─────────┬───────┘            └─────────────────┘
          │ 1:M                          │ 1:M
          ▼                              ▼
┌─────────────────┐            ┌─────────────────┐
│ Tracking Links  │            │   Campaigns     │
└─────────┬───────┘            └─────────┬───────┘
          │ 1:M                          │ 1:M
          ▼                              ▼
┌─────────────────┐            ┌─────────────────┐
│  Click Events   │            │ Tracking Links  │
└─────────┬───────┘            │   (Campaign)    │
          │ 1:1                └─────────────────┘
          ▼
┌─────────────────┐    M:1     ┌─────────────────┐
│  Commissions    │◄──────────►│    Payouts      │
└─────────────────┘            └─────────────────┘

┌─────────────────┐    1:M     ┌─────────────────┐
│     Users       │◄──────────►│   API Keys      │
└─────────────────┘            └─────────────────┘

┌─────────────────┐    1:M     ┌─────────────────┐
│     Users       │◄──────────►│ Webhook         │
└─────────────────┘            │  Endpoints      │
                               └─────────────────┘
```

## 📋 Detailed Entity Relationships

### **👤 User-Centric Relationships**

#### **Users (1) ↔ (1) User Settings**
- **Type**: One-to-One
- **Description**: Each user has exactly one settings record
- **Foreign Key**: `user_settings.user_id → users.id`
- **Purpose**: Store user preferences and onboarding status

#### **Users (1) ↔ (1) Subscriptions**
- **Type**: One-to-One
- **Description**: Each user has one active subscription
- **Foreign Key**: `subscriptions.user_id → users.id`
- **Purpose**: Billing and quota management

#### **Users (1) ↔ (M) Workspaces**
- **Type**: One-to-Many
- **Description**: User can own multiple workspaces
- **Foreign Key**: `workspaces.owner_id → users.id`
- **Purpose**: Multi-tenant organization management

#### **Users (1) ↔ (M) Notifications**
- **Type**: One-to-Many
- **Description**: User receives multiple notifications
- **Foreign Key**: `notifications.user_id → users.id`
- **Purpose**: Communication and alerts

### **🔗 Affiliate Program Relationships**

#### **Affiliate Networks (1) ↔ (M) Affiliate Programs**
- **Type**: One-to-Many
- **Description**: Each network has multiple programs
- **Foreign Key**: `affiliate_programs.network_id → affiliate_networks.id`
- **Purpose**: Organize programs by source network

#### **Users (M) ↔ (M) Affiliate Programs** (via User Program Enrollments)
- **Type**: Many-to-Many
- **Description**: Users can join multiple programs, programs have multiple users
- **Junction Table**: `user_program_enrollments`
- **Foreign Keys**: 
  - `user_program_enrollments.user_id → users.id`
  - `user_program_enrollments.program_id → affiliate_programs.id`
- **Purpose**: Track program memberships and approval status

### **📊 Campaign & Tracking Relationships**

#### **Users (1) ↔ (M) Campaigns**
- **Type**: One-to-Many
- **Description**: User creates multiple campaigns
- **Foreign Key**: `campaigns.user_id → users.id`
- **Purpose**: Organize marketing efforts

#### **Campaigns (1) ↔ (M) Tracking Links**
- **Type**: One-to-Many (Optional)
- **Description**: Campaign can contain multiple tracking links
- **Foreign Key**: `tracking_links.campaign_id → campaigns.id`
- **Purpose**: Group links by marketing campaign

#### **Affiliate Programs (1) ↔ (M) Tracking Links**
- **Type**: One-to-Many
- **Description**: Each program can have multiple tracking links
- **Foreign Key**: `tracking_links.program_id → affiliate_programs.id`
- **Purpose**: Link generation for specific programs

#### **Tracking Links (1) ↔ (M) Click Events**
- **Type**: One-to-Many
- **Description**: Each link generates multiple click events
- **Foreign Key**: `click_events.tracking_link_id → tracking_links.id`
- **Purpose**: Track individual clicks and conversions

### **💰 Financial Relationships**

#### **Click Events (1) ↔ (1) Commissions** (Optional)
- **Type**: One-to-One (Optional)
- **Description**: Converting clicks generate commissions
- **Foreign Key**: `commissions.click_event_id → click_events.id`
- **Purpose**: Link earnings to specific clicks

#### **Users (1) ↔ (M) Commissions**
- **Type**: One-to-Many
- **Description**: User earns multiple commissions
- **Foreign Key**: `commissions.user_id → users.id`
- **Purpose**: Track user earnings

#### **Affiliate Programs (1) ↔ (M) Commissions**
- **Type**: One-to-Many
- **Description**: Program generates multiple commissions
- **Foreign Key**: `commissions.program_id → affiliate_programs.id`
- **Purpose**: Track program performance

#### **Payouts (1) ↔ (M) Commissions**
- **Type**: One-to-Many
- **Description**: Payout includes multiple commissions
- **Foreign Key**: `commissions.payout_id → payouts.id`
- **Purpose**: Batch commission payments

#### **Users (1) ↔ (M) Payouts**
- **Type**: One-to-Many
- **Description**: User receives multiple payouts
- **Foreign Key**: `payouts.user_id → users.id`
- **Purpose**: Payment history tracking

### **🔧 API & Integration Relationships**

#### **Users (1) ↔ (M) API Keys**
- **Type**: One-to-Many
- **Description**: User can have multiple API keys
- **Foreign Key**: `api_keys.user_id → users.id`
- **Purpose**: Developer access management

#### **Workspaces (1) ↔ (M) API Keys** (Optional)
- **Type**: One-to-Many (Optional)
- **Description**: API keys can be workspace-scoped
- **Foreign Key**: `api_keys.workspace_id → workspaces.id`
- **Purpose**: Team-based API access

#### **Users (1) ↔ (M) Webhook Endpoints**
- **Type**: One-to-Many
- **Description**: User can configure multiple webhooks
- **Foreign Key**: `webhook_endpoints.user_id → users.id`
- **Purpose**: Real-time integration notifications

## 📊 Relationship Summary Table

| Entity 1 | Relationship | Entity 2 | Type | Purpose |
|----------|--------------|----------|------|---------|
| Users | 1:1 | User Settings | One-to-One | User preferences |
| Users | 1:1 | Subscriptions | One-to-One | Billing management |
| Users | 1:M | Workspaces | One-to-Many | Organization ownership |
| Users | 1:M | Notifications | One-to-Many | Communication |
| Affiliate Networks | 1:M | Affiliate Programs | One-to-Many | Program organization |
| Users | M:M | Affiliate Programs | Many-to-Many | Program enrollment |
| Users | 1:M | Campaigns | One-to-Many | Campaign management |
| Campaigns | 1:M | Tracking Links | One-to-Many | Link organization |
| Affiliate Programs | 1:M | Tracking Links | One-to-Many | Program-specific links |
| Tracking Links | 1:M | Click Events | One-to-Many | Click tracking |
| Click Events | 1:1 | Commissions | One-to-One (Optional) | Conversion tracking |
| Users | 1:M | Commissions | One-to-Many | Earnings tracking |
| Affiliate Programs | 1:M | Commissions | One-to-Many | Program performance |
| Payouts | 1:M | Commissions | One-to-Many | Payment batching |
| Users | 1:M | Payouts | One-to-Many | Payment history |
| Users | 1:M | API Keys | One-to-Many | API access |
| Workspaces | 1:M | API Keys | One-to-Many (Optional) | Team API access |
| Users | 1:M | Webhook Endpoints | One-to-Many | Integration hooks |

## 🎯 Key Design Patterns

### **1. Multi-tenancy via Workspaces**
- Users can own multiple workspaces
- Workspaces isolate data and permissions
- Supports agency and team use cases

### **2. Flexible Program Enrollment**
- Many-to-many relationship via junction table
- Tracks application status and approval
- Supports custom commission rates

### **3. Hierarchical Campaign Organization**
- Campaigns group related tracking links
- Optional relationship allows standalone links
- Supports both organized and ad-hoc marketing

### **4. Complete Audit Trail**
- All entities have created_at/updated_at
- Click events link to commissions
- Full tracking from click to payout

### **5. Extensible Integration System**
- API keys support multiple permission levels
- Webhooks enable real-time integrations
- Workspace-scoped access for teams

## 🔍 Data Flow Examples

### **User Journey: From Click to Payout**
1. **User** creates **Tracking Link** for **Affiliate Program**
2. **Click Event** is generated when link is clicked
3. **Commission** is created when click converts
4. **Commission** is included in **Payout** when threshold is met

### **Program Discovery Flow**
1. **Affiliate Network** provides **Affiliate Programs**
2. **User** applies via **User Program Enrollment**
3. **User** creates **Tracking Links** for approved programs
4. **Click Events** and **Commissions** track performance

### **Team Collaboration Flow**
1. **User** creates **Workspace** for team
2. **API Keys** are generated for workspace access
3. **Campaigns** and **Tracking Links** are workspace-scoped
4. **Webhook Endpoints** notify team systems of events

This comprehensive relationship structure ensures data integrity, supports complex business logic, and provides flexibility for future enhancements.
