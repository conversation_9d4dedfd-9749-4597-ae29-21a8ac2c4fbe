# Backend API Specification
## FastAPI Implementation for Affiliate Middleware Platform

This document outlines all API endpoints that need to be implemented for the affiliate middleware platform.

## 📋 API Overview

### Base Configuration
- **Framework**: FastAPI
- **Authentication**: JWT Bearer tokens + API Keys
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Documentation**: Auto-generated OpenAPI/Swagger
- **Rate Limiting**: Redis-based with user-specific limits

### API Structure
```
/api/v1/
├── auth/          # Authentication endpoints
├── users/         # User management
├── networks/      # Affiliate networks
├── programs/      # Affiliate programs
├── enrollments/   # Program enrollments
├── campaigns/     # Campaign management
├── links/         # Tracking links
├── analytics/     # Analytics and reporting
├── commissions/   # Commission tracking
├── payouts/       # Payout management
├── webhooks/      # Webhook management
└── admin/         # Admin-only endpoints
```

## 🔐 Authentication Endpoints

### POST /api/v1/auth/login
**Purpose**: User authentication
```python
# Request
{
    "email": "<EMAIL>",
    "password": "password123"
}

# Response
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
        "id": "user_123",
        "email": "<EMAIL>",
        "name": "John Doe",
        "user_type": "individual",
        "affiliate_id": "AFF_12345678"
    }
}
```

### POST /api/v1/auth/refresh
**Purpose**: Refresh access token
```python
# Request
{
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}

# Response
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600
}
```

### POST /api/v1/auth/register
**Purpose**: User registration with affiliate setup
```python
# Request
{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Jane",
    "lastName": "Smith",
    "user_type": "individual",
    "payout_method": "stripe",
    "terms_accepted": true
}

# Response
{
    "message": "Registration successful",
    "user_id": "user_456",
    "affiliate_id": "AFF_87654321",
    "verification_required": true
}
```

## 👥 User Management Endpoints

### GET /api/v1/users/profile
**Purpose**: Get current user profile
```python
# Response
{
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "user_type": "individual",
    "kyc_status": "verified",
    "affiliate_id": "AFF_12345678",
    "payout_method": "stripe",
    "total_earnings": 1250.75,
    "pending_earnings": 125.50,
    "lifetime_clicks": 15420,
    "lifetime_conversions": 342,
    "created_at": "2024-01-15T10:30:00Z"
}
```

### PUT /api/v1/users/profile
**Purpose**: Update user profile
```python
# Request
{
    "name": "John",
    "lastName": "Doe Updated",
    "payout_method": "paypal",
    "payout_details": {
        "paypal_email": "<EMAIL>"
    }
}

# Response
{
    "message": "Profile updated successfully",
    "user": { /* updated user object */ }
}
```

### GET /api/v1/users/settings
**Purpose**: Get user settings and preferences
```python
# Response
{
    "notifications": {
        "email_new_programs": true,
        "email_commissions": true,
        "email_payouts": true
    },
    "preferences": {
        "default_commission_display": "net",
        "auto_apply_programs": false,
        "preferred_payout_frequency": "monthly",
        "analytics_timezone": "UTC"
    }
}
```

## 🌐 Affiliate Networks Endpoints

### GET /api/v1/networks
**Purpose**: List all available affiliate networks
```python
# Response
{
    "networks": [
        {
            "id": "net_123",
            "name": "AWIN",
            "slug": "awin",
            "description": "Global affiliate network with 15,000+ advertisers",
            "is_active": true,
            "supported_features": {
                "tracking": true,
                "reporting": true,
                "real_time": false
            },
            "logo_url": "https://example.com/logos/awin.png",
            "program_count": 1250
        }
    ],
    "total": 6,
    "page": 1,
    "per_page": 20
}
```

### GET /api/v1/networks/{network_id}
**Purpose**: Get detailed network information
```python
# Response
{
    "id": "net_123",
    "name": "AWIN",
    "slug": "awin",
    "description": "Global affiliate network with 15,000+ advertisers",
    "api_endpoint": "https://api.awin.com/v1",
    "auth_type": "api_key",
    "supported_features": {
        "tracking": true,
        "reporting": true,
        "real_time": false,
        "fraud_detection": true
    },
    "rate_limit": 1000,
    "documentation_url": "https://wiki.awin.com/index.php/API",
    "program_count": 1250,
    "active_programs": 1180
}
```

## 📊 Affiliate Programs Endpoints

### GET /api/v1/programs
**Purpose**: List affiliate programs with filtering
```python
# Query Parameters
# ?category=fashion&commission_min=5&status=active&network=awin&page=1&per_page=20

# Response
{
    "programs": [
        {
            "id": "prog_123",
            "network": {
                "id": "net_123",
                "name": "AWIN",
                "slug": "awin"
            },
            "name": "Nike Affiliate Program",
            "brand": "Nike",
            "category": "Fashion & Sports",
            "commission_type": "percentage",
            "commission_rate": 5.50,
            "cookie_duration": 30,
            "status": "active",
            "min_payout": 25.00,
            "avg_earnings_per_click": 0.0125,
            "conversion_rate": 0.0340,
            "logo_url": "https://example.com/logos/nike.png",
            "tags": ["sports", "fashion", "footwear"],
            "enrollment_status": "not_enrolled"
        }
    ],
    "total": 1250,
    "page": 1,
    "per_page": 20,
    "filters": {
        "categories": ["Fashion & Sports", "Electronics", "Travel"],
        "commission_types": ["percentage", "fixed", "tiered"],
        "networks": ["awin", "cj-affiliate", "rakuten"]
    }
}
```

### GET /api/v1/programs/{program_id}
**Purpose**: Get detailed program information
```python
# Response
{
    "id": "prog_123",
    "network": {
        "id": "net_123",
        "name": "AWIN",
        "slug": "awin"
    },
    "external_id": "PROG_NIKE_001",
    "name": "Nike Affiliate Program",
    "brand": "Nike",
    "description": "Join Nike's affiliate program and earn commissions...",
    "category": "Fashion & Sports",
    "commission_type": "percentage",
    "commission_rate": 5.50,
    "commission_structure": {
        "base_rate": 5.5,
        "premium_rate": 7.0,
        "threshold": 1000
    },
    "cookie_duration": 30,
    "payout_terms": "Net 30 days",
    "restrictions": {
        "geographic": ["US", "CA", "GB"],
        "content_types": ["review", "comparison", "social"]
    },
    "status": "active",
    "min_payout": 25.00,
    "avg_earnings_per_click": 0.0125,
    "conversion_rate": 0.0340,
    "logo_url": "https://example.com/logos/nike.png",
    "website_url": "https://nike.com",
    "terms_url": "https://nike.com/affiliate-terms",
    "tags": ["sports", "fashion", "footwear"],
    "enrollment_status": "approved",
    "enrollment_date": "2024-01-20T15:30:00Z",
    "tracking_id": "TRK_NIKE_USER123"
}
```

### POST /api/v1/programs/search
**Purpose**: Advanced program search with complex filters
```python
# Request
{
    "query": "fashion",
    "filters": {
        "categories": ["Fashion & Sports", "Beauty"],
        "commission_min": 5.0,
        "commission_max": 15.0,
        "commission_types": ["percentage"],
        "networks": ["awin", "cj-affiliate"],
        "cookie_duration_min": 30,
        "min_payout_max": 50.0,
        "tags": ["fashion", "clothing"]
    },
    "sort": {
        "field": "commission_rate",
        "direction": "desc"
    },
    "page": 1,
    "per_page": 20
}

# Response
{
    "programs": [ /* array of programs */ ],
    "total": 45,
    "page": 1,
    "per_page": 20,
    "search_time_ms": 125
}
```

## 🎯 Program Enrollments Endpoints

### GET /api/v1/enrollments
**Purpose**: Get user's program enrollments
```python
# Query Parameters: ?status=approved&page=1&per_page=20

# Response
{
    "enrollments": [
        {
            "id": "enr_123",
            "program": {
                "id": "prog_123",
                "name": "Nike Affiliate Program",
                "brand": "Nike",
                "commission_rate": 5.50,
                "logo_url": "https://example.com/logos/nike.png"
            },
            "status": "approved",
            "application_date": "2024-01-20T15:30:00Z",
            "approval_date": "2024-01-22T10:15:00Z",
            "tracking_id": "TRK_NIKE_USER123",
            "custom_commission_rate": null,
            "stats": {
                "total_clicks": 1250,
                "total_conversions": 45,
                "total_commission": 567.80,
                "pending_commission": 125.50
            }
        }
    ],
    "total": 12,
    "page": 1,
    "per_page": 20
}
```

### POST /api/v1/enrollments
**Purpose**: Apply to join an affiliate program
```python
# Request
{
    "program_id": "prog_123",
    "workspace_id": "ws_456",  # optional
    "application_notes": "I run a fashion blog with 50k monthly visitors"
}

# Response
{
    "enrollment_id": "enr_789",
    "status": "pending",
    "message": "Application submitted successfully",
    "estimated_review_time": "2-5 business days"
}
```

### PUT /api/v1/enrollments/{enrollment_id}
**Purpose**: Update enrollment (mainly for status changes)
```python
# Request
{
    "notes": "Updated tracking setup completed"
}

# Response
{
    "message": "Enrollment updated successfully",
    "enrollment": { /* updated enrollment object */ }
}
```

### DELETE /api/v1/enrollments/{enrollment_id}
**Purpose**: Cancel/leave a program
```python
# Response
{
    "message": "Successfully left the affiliate program",
    "final_stats": {
        "total_earnings": 567.80,
        "pending_commission": 125.50,
        "payout_status": "will_be_processed"
    }
}
```

## 🚀 Campaigns Endpoints

### GET /api/v1/campaigns
**Purpose**: List user's campaigns
```python
# Response
{
    "campaigns": [
        {
            "id": "camp_123",
            "name": "Summer Fashion Campaign",
            "description": "Promoting summer clothing and accessories",
            "status": "active",
            "start_date": "2024-06-01T00:00:00Z",
            "end_date": "2024-08-31T23:59:59Z",
            "budget": 1500.00,
            "target_clicks": 5000,
            "target_conversions": 150,
            "stats": {
                "total_links": 25,
                "total_clicks": 3420,
                "total_conversions": 89,
                "total_commission": 445.60,
                "conversion_rate": 0.026
            },
            "tags": ["fashion", "summer", "clothing"]
        }
    ],
    "total": 8,
    "page": 1,
    "per_page": 20
}
```

### POST /api/v1/campaigns
**Purpose**: Create new campaign
```python
# Request
{
    "name": "Black Friday Electronics",
    "description": "Electronics deals for Black Friday",
    "start_date": "2024-11-20T00:00:00Z",
    "end_date": "2024-11-30T23:59:59Z",
    "budget": 2000.00,
    "target_clicks": 8000,
    "target_conversions": 200,
    "tags": ["electronics", "black-friday", "deals"],
    "utm_source": "website",
    "utm_medium": "affiliate",
    "utm_campaign": "bf_electronics_2024"
}

# Response
{
    "campaign_id": "camp_456",
    "message": "Campaign created successfully",
    "campaign": { /* created campaign object */ }
}
```

### GET /api/v1/campaigns/{campaign_id}
**Purpose**: Get detailed campaign information
```python
# Response
{
    "id": "camp_123",
    "name": "Summer Fashion Campaign",
    "description": "Promoting summer clothing and accessories",
    "status": "active",
    "start_date": "2024-06-01T00:00:00Z",
    "end_date": "2024-08-31T23:59:59Z",
    "budget": 1500.00,
    "target_clicks": 5000,
    "target_conversions": 150,
    "utm_source": "website",
    "utm_medium": "affiliate",
    "utm_campaign": "summer_fashion_2024",
    "tags": ["fashion", "summer", "clothing"],
    "stats": {
        "total_links": 25,
        "total_clicks": 3420,
        "total_conversions": 89,
        "total_commission": 445.60,
        "conversion_rate": 0.026,
        "avg_commission_per_click": 0.13,
        "budget_spent": 445.60,
        "budget_remaining": 1054.40
    },
    "links": [
        {
            "id": "link_123",
            "tracking_url": "https://track.affiliate-platform.com/abc123",
            "short_code": "ABC123",
            "program": "Nike",
            "clicks": 450,
            "conversions": 12,
            "commission": 67.80
        }
    ],
    "created_at": "2024-05-15T10:30:00Z",
    "updated_at": "2024-07-20T14:45:00Z"
}
```

### PUT /api/v1/campaigns/{campaign_id}
**Purpose**: Update campaign
```python
# Request
{
    "name": "Summer Fashion Campaign - Extended",
    "end_date": "2024-09-15T23:59:59Z",
    "budget": 2000.00,
    "status": "active"
}

# Response
{
    "message": "Campaign updated successfully",
    "campaign": { /* updated campaign object */ }
}
```

### DELETE /api/v1/campaigns/{campaign_id}
**Purpose**: Archive/delete campaign
```python
# Response
{
    "message": "Campaign archived successfully",
    "final_stats": {
        "total_clicks": 3420,
        "total_conversions": 89,
        "total_commission": 445.60,
        "links_affected": 25
    }
}
```

## 🔗 Tracking Links Endpoints

### GET /api/v1/links
**Purpose**: List user's tracking links
```python
# Query Parameters: ?campaign_id=camp_123&program_id=prog_456&is_active=true&page=1&per_page=20

# Response
{
    "links": [
        {
            "id": "link_123",
            "program": {
                "id": "prog_123",
                "name": "Nike Affiliate Program",
                "brand": "Nike"
            },
            "campaign": {
                "id": "camp_123",
                "name": "Summer Fashion Campaign"
            },
            "original_url": "https://nike.com/product/air-max-90",
            "tracking_url": "https://track.affiliate-platform.com/abc123",
            "short_code": "ABC123",
            "link_type": "direct",
            "is_active": true,
            "expires_at": null,
            "stats": {
                "click_count": 450,
                "conversion_count": 12,
                "total_commission": 67.80,
                "last_click_at": "2024-07-20T14:30:00Z"
            },
            "created_at": "2024-06-01T10:00:00Z"
        }
    ],
    "total": 156,
    "page": 1,
    "per_page": 20
}
```

### POST /api/v1/links
**Purpose**: Create new tracking link
```python
# Request
{
    "program_id": "prog_123",
    "campaign_id": "camp_123",  # optional
    "original_url": "https://nike.com/product/air-max-90",
    "link_type": "direct",
    "custom_parameters": {
        "utm_content": "air-max-90",
        "utm_term": "running-shoes"
    },
    "expires_at": "2024-12-31T23:59:59Z"  # optional
}

# Response
{
    "link_id": "link_456",
    "tracking_url": "https://track.affiliate-platform.com/def456",
    "short_code": "DEF456",
    "qr_code_url": "https://api.qrserver.com/v1/create-qr-code/?data=...",
    "embed_codes": {
        "html": "<a href='https://track.affiliate-platform.com/def456'>Nike Air Max 90</a>",
        "iframe": "<iframe src='https://track.affiliate-platform.com/widget/def456'></iframe>",
        "javascript": "<script src='https://track.affiliate-platform.com/js/def456.js'></script>"
    },
    "message": "Tracking link created successfully"
}
```

### GET /api/v1/links/{link_id}
**Purpose**: Get detailed link information and analytics
```python
# Response
{
    "id": "link_123",
    "program": {
        "id": "prog_123",
        "name": "Nike Affiliate Program",
        "brand": "Nike",
        "commission_rate": 5.50
    },
    "campaign": {
        "id": "camp_123",
        "name": "Summer Fashion Campaign"
    },
    "original_url": "https://nike.com/product/air-max-90",
    "tracking_url": "https://track.affiliate-platform.com/abc123",
    "short_code": "ABC123",
    "link_type": "direct",
    "custom_parameters": {
        "utm_source": "affiliate",
        "utm_medium": "link",
        "utm_campaign": "summer_fashion_2024",
        "utm_content": "air-max-90"
    },
    "is_active": true,
    "expires_at": null,
    "stats": {
        "click_count": 450,
        "conversion_count": 12,
        "total_commission": 67.80,
        "last_click_at": "2024-07-20T14:30:00Z",
        "conversion_rate": 0.0267,
        "avg_commission_per_click": 0.15
    },
    "analytics": {
        "clicks_by_day": [
            {"date": "2024-07-15", "clicks": 25, "conversions": 1},
            {"date": "2024-07-16", "clicks": 32, "conversions": 0}
        ],
        "top_referrers": [
            {"referrer": "google.com", "clicks": 180},
            {"referrer": "facebook.com", "clicks": 95}
        ],
        "device_breakdown": {
            "desktop": 60,
            "mobile": 35,
            "tablet": 5
        },
        "geographic_data": [
            {"country": "US", "clicks": 280},
            {"country": "CA", "clicks": 95}
        ]
    },
    "embed_codes": {
        "html": "<a href='https://track.affiliate-platform.com/abc123'>Nike Air Max 90</a>",
        "iframe": "<iframe src='https://track.affiliate-platform.com/widget/abc123'></iframe>",
        "javascript": "<script src='https://track.affiliate-platform.com/js/abc123.js'></script>"
    },
    "qr_code_url": "https://api.qrserver.com/v1/create-qr-code/?data=...",
    "created_at": "2024-06-01T10:00:00Z",
    "updated_at": "2024-07-20T14:30:00Z"
}
```

### PUT /api/v1/links/{link_id}
**Purpose**: Update tracking link
```python
# Request
{
    "is_active": false,
    "expires_at": "2024-12-31T23:59:59Z",
    "custom_parameters": {
        "utm_content": "updated-content"
    }
}

# Response
{
    "message": "Link updated successfully",
    "link": { /* updated link object */ }
}
```

### DELETE /api/v1/links/{link_id}
**Purpose**: Deactivate/delete tracking link
```python
# Response
{
    "message": "Link deactivated successfully",
    "final_stats": {
        "total_clicks": 450,
        "total_conversions": 12,
        "total_commission": 67.80
    }
}
```

## 📊 Analytics Endpoints

### GET /api/v1/analytics/dashboard
**Purpose**: Get dashboard overview analytics
```python
# Query Parameters: ?period=30d&timezone=UTC

# Response
{
    "period": "30d",
    "summary": {
        "total_clicks": 15420,
        "total_conversions": 342,
        "total_commission": 2567.80,
        "pending_commission": 456.20,
        "conversion_rate": 0.0222,
        "avg_commission_per_click": 0.1665
    },
    "trends": {
        "clicks_change": 15.2,
        "conversions_change": 8.7,
        "commission_change": 22.1
    },
    "charts": {
        "clicks_by_day": [
            {"date": "2024-07-01", "clicks": 245, "conversions": 8, "commission": 45.60},
            {"date": "2024-07-02", "clicks": 198, "conversions": 5, "commission": 32.40}
        ],
        "top_programs": [
            {
                "program_id": "prog_123",
                "name": "Nike",
                "clicks": 3420,
                "conversions": 89,
                "commission": 567.80
            }
        ],
        "performance_by_device": {
            "desktop": {"clicks": 9252, "conversions": 205, "rate": 0.0221},
            "mobile": {"clicks": 5396, "conversions": 118, "rate": 0.0219},
            "tablet": {"clicks": 772, "conversions": 19, "rate": 0.0246}
        }
    }
}
```

### GET /api/v1/analytics/programs
**Purpose**: Get program-specific analytics
```python
# Query Parameters: ?program_id=prog_123&period=30d&group_by=day

# Response
{
    "program": {
        "id": "prog_123",
        "name": "Nike Affiliate Program",
        "brand": "Nike"
    },
    "period": "30d",
    "summary": {
        "total_clicks": 3420,
        "total_conversions": 89,
        "total_commission": 567.80,
        "conversion_rate": 0.026,
        "avg_commission_per_conversion": 6.38
    },
    "data": [
        {"date": "2024-07-01", "clicks": 125, "conversions": 3, "commission": 18.45},
        {"date": "2024-07-02", "clicks": 98, "conversions": 2, "commission": 12.30}
    ],
    "top_links": [
        {
            "link_id": "link_123",
            "short_code": "ABC123",
            "clicks": 450,
            "conversions": 12,
            "commission": 67.80
        }
    ]
}
```

### GET /api/v1/analytics/campaigns
**Purpose**: Get campaign analytics
```python
# Query Parameters: ?campaign_id=camp_123&period=30d

# Response
{
    "campaign": {
        "id": "camp_123",
        "name": "Summer Fashion Campaign"
    },
    "period": "30d",
    "summary": {
        "total_clicks": 3420,
        "total_conversions": 89,
        "total_commission": 445.60,
        "budget_spent": 445.60,
        "budget_remaining": 1054.40,
        "roi": 0.297
    },
    "performance": [
        {"date": "2024-07-01", "clicks": 125, "conversions": 3, "commission": 18.45},
        {"date": "2024-07-02", "clicks": 98, "conversions": 2, "commission": 12.30}
    ],
    "link_performance": [
        {
            "link_id": "link_123",
            "program": "Nike",
            "clicks": 450,
            "conversions": 12,
            "commission": 67.80
        }
    ]
}
```

### POST /api/v1/analytics/reports
**Purpose**: Generate custom analytics report
```python
# Request
{
    "report_type": "commission_summary",
    "date_range": {
        "start": "2024-06-01",
        "end": "2024-07-31"
    },
    "filters": {
        "programs": ["prog_123", "prog_456"],
        "campaigns": ["camp_123"],
        "status": ["confirmed", "paid"]
    },
    "group_by": "month",
    "format": "csv"  # or "pdf", "json"
}

# Response
{
    "report_id": "rep_789",
    "status": "generating",
    "estimated_completion": "2024-07-20T15:05:00Z",
    "download_url": null,
    "message": "Report generation started"
}
```

### GET /api/v1/analytics/reports/{report_id}
**Purpose**: Get report status and download link
```python
# Response
{
    "report_id": "rep_789",
    "status": "completed",
    "generated_at": "2024-07-20T15:03:45Z",
    "download_url": "https://api.affiliate-platform.com/downloads/rep_789.csv",
    "expires_at": "2024-07-27T15:03:45Z",
    "file_size": 2048576,
    "record_count": 1250
}
```

## 💰 Commissions Endpoints

### GET /api/v1/commissions
**Purpose**: List user's commissions
```python
# Query Parameters: ?status=confirmed&program_id=prog_123&date_from=2024-06-01&page=1&per_page=20

# Response
{
    "commissions": [
        {
            "id": "comm_123",
            "program": {
                "id": "prog_123",
                "name": "Nike Affiliate Program",
                "brand": "Nike"
            },
            "tracking_link": {
                "id": "link_123",
                "short_code": "ABC123"
            },
            "amount": 12.45,
            "currency": "USD",
            "commission_type": "conversion",
            "status": "confirmed",
            "transaction_date": "2024-07-15T14:30:00Z",
            "confirmed_at": "2024-07-17T10:15:00Z",
            "order_value": 249.00,
            "commission_rate": 5.0,
            "external_transaction_id": "TXN_NIKE_789456"
        }
    ],
    "total": 342,
    "page": 1,
    "per_page": 20,
    "summary": {
        "total_amount": 2567.80,
        "pending_amount": 456.20,
        "confirmed_amount": 1890.35,
        "paid_amount": 221.25
    }
}
```

### GET /api/v1/commissions/{commission_id}
**Purpose**: Get detailed commission information
```python
# Response
{
    "id": "comm_123",
    "program": {
        "id": "prog_123",
        "name": "Nike Affiliate Program",
        "brand": "Nike",
        "commission_rate": 5.0
    },
    "tracking_link": {
        "id": "link_123",
        "short_code": "ABC123",
        "original_url": "https://nike.com/product/air-max-90"
    },
    "click_event": {
        "id": "click_456",
        "clicked_at": "2024-07-15T14:25:00Z",
        "country": "US",
        "device": "desktop",
        "referrer": "google.com"
    },
    "amount": 12.45,
    "currency": "USD",
    "commission_type": "conversion",
    "status": "confirmed",
    "transaction_date": "2024-07-15T14:30:00Z",
    "confirmed_at": "2024-07-17T10:15:00Z",
    "paid_at": null,
    "payout_id": null,
    "order_value": 249.00,
    "commission_rate": 5.0,
    "external_transaction_id": "TXN_NIKE_789456",
    "notes": "Standard conversion commission",
    "created_at": "2024-07-15T14:30:00Z",
    "updated_at": "2024-07-17T10:15:00Z"
}
```

## 💳 Payouts Endpoints

### GET /api/v1/payouts
**Purpose**: List user's payouts
```python
# Response
{
    "payouts": [
        {
            "id": "payout_123",
            "amount": 567.80,
            "currency": "USD",
            "method": "stripe",
            "status": "completed",
            "scheduled_at": "2024-07-01T00:00:00Z",
            "processed_at": "2024-07-01T15:30:00Z",
            "fees": 5.68,
            "net_amount": 562.12,
            "commission_count": 45,
            "external_transaction_id": "pi_1234567890"
        }
    ],
    "total": 8,
    "page": 1,
    "per_page": 20,
    "summary": {
        "total_paid": 2890.45,
        "pending_amount": 456.20,
        "next_payout_date": "2024-08-01T00:00:00Z",
        "next_payout_amount": 456.20
    }
}
```

### POST /api/v1/payouts/request
**Purpose**: Request manual payout
```python
# Request
{
    "amount": 456.20,
    "method": "paypal",
    "notes": "Emergency payout request"
}

# Response
{
    "payout_id": "payout_456",
    "status": "pending",
    "estimated_processing_time": "2-3 business days",
    "fees": 4.56,
    "net_amount": 451.64,
    "message": "Payout request submitted successfully"
}
```

### GET /api/v1/payouts/{payout_id}
**Purpose**: Get detailed payout information
```python
# Response
{
    "id": "payout_123",
    "amount": 567.80,
    "currency": "USD",
    "method": "stripe",
    "status": "completed",
    "scheduled_at": "2024-07-01T00:00:00Z",
    "processed_at": "2024-07-01T15:30:00Z",
    "fees": 5.68,
    "net_amount": 562.12,
    "external_transaction_id": "pi_1234567890",
    "commissions": [
        {
            "id": "comm_123",
            "amount": 12.45,
            "program": "Nike",
            "transaction_date": "2024-06-15T14:30:00Z"
        }
    ],
    "commission_count": 45,
    "notes": null,
    "created_at": "2024-07-01T00:00:00Z",
    "updated_at": "2024-07-01T15:30:00Z"
}
```

## 🔗 Webhooks Endpoints

### GET /api/v1/webhooks
**Purpose**: List user's webhook endpoints
```python
# Response
{
    "webhooks": [
        {
            "id": "webhook_123",
            "url": "https://mysite.com/webhooks/affiliate",
            "events": ["commission.confirmed", "payout.completed"],
            "is_active": true,
            "last_triggered_at": "2024-07-20T14:30:00Z",
            "failure_count": 0,
            "created_at": "2024-06-01T10:00:00Z"
        }
    ],
    "total": 3
}
```

### POST /api/v1/webhooks
**Purpose**: Create webhook endpoint
```python
# Request
{
    "url": "https://mysite.com/webhooks/affiliate",
    "events": ["commission.confirmed", "commission.paid", "payout.completed"],
    "secret": "webhook_secret_key_123"
}

# Response
{
    "webhook_id": "webhook_456",
    "message": "Webhook created successfully",
    "test_url": "https://api.affiliate-platform.com/webhooks/test/webhook_456"
}
```

### PUT /api/v1/webhooks/{webhook_id}
**Purpose**: Update webhook endpoint
```python
# Request
{
    "events": ["commission.confirmed", "commission.paid"],
    "is_active": true
}

# Response
{
    "message": "Webhook updated successfully",
    "webhook": { /* updated webhook object */ }
}
```

### POST /api/v1/webhooks/{webhook_id}/test
**Purpose**: Test webhook endpoint
```python
# Response
{
    "test_id": "test_789",
    "status": "success",
    "response_code": 200,
    "response_time_ms": 245,
    "message": "Webhook test successful"
}
```

## 🔑 API Keys Endpoints

### GET /api/v1/api-keys
**Purpose**: List user's API keys
```python
# Response
{
    "api_keys": [
        {
            "id": "key_123",
            "name": "WordPress Plugin Key",
            "permissions": ["read:programs", "read:analytics", "write:links"],
            "is_active": true,
            "last_used_at": "2024-07-20T14:30:00Z",
            "usage_count": 1250,
            "rate_limit": 1000,
            "expires_at": "2025-07-20T00:00:00Z",
            "created_at": "2024-01-20T10:00:00Z"
        }
    ],
    "total": 3
}
```

### POST /api/v1/api-keys
**Purpose**: Create new API key
```python
# Request
{
    "name": "Mobile App Key",
    "permissions": ["read:programs", "read:analytics"],
    "rate_limit": 500,
    "expires_at": "2025-12-31T23:59:59Z"
}

# Response
{
    "api_key_id": "key_456",
    "api_key": "sk_live_1234567890abcdef...",
    "message": "API key created successfully",
    "warning": "This is the only time the full API key will be shown"
}
```

### PUT /api/v1/api-keys/{key_id}
**Purpose**: Update API key
```python
# Request
{
    "name": "Updated Mobile App Key",
    "is_active": false
}

# Response
{
    "message": "API key updated successfully"
}
```

### DELETE /api/v1/api-keys/{key_id}
**Purpose**: Revoke API key
```python
# Response
{
    "message": "API key revoked successfully"
}
```

## 🛠️ Admin Endpoints

### GET /api/v1/admin/users
**Purpose**: List all users (admin only)
```python
# Query Parameters: ?user_type=agency&kyc_status=pending&page=1&per_page=20

# Response
{
    "users": [
        {
            "id": "user_123",
            "email": "<EMAIL>",
            "name": "John Doe",
            "user_type": "agency",
            "kyc_status": "pending",
            "total_earnings": 2567.80,
            "lifetime_clicks": 15420,
            "created_at": "2024-01-15T10:30:00Z",
            "last_active": "2024-07-20T14:30:00Z"
        }
    ],
    "total": 1250,
    "page": 1,
    "per_page": 20
}
```

### PUT /api/v1/admin/users/{user_id}/kyc
**Purpose**: Update user KYC status
```python
# Request
{
    "kyc_status": "verified",
    "notes": "Documents verified successfully"
}

# Response
{
    "message": "KYC status updated successfully",
    "user_notified": true
}
```

### GET /api/v1/admin/programs/sync
**Purpose**: Trigger program sync from external networks
```python
# Query Parameters: ?network=awin&force=true

# Response
{
    "sync_id": "sync_789",
    "status": "started",
    "estimated_completion": "2024-07-20T15:30:00Z",
    "networks": ["awin"],
    "message": "Program sync initiated"
}
```

### GET /api/v1/admin/analytics/platform
**Purpose**: Get platform-wide analytics
```python
# Response
{
    "summary": {
        "total_users": 1250,
        "active_users": 890,
        "total_programs": 2500,
        "total_clicks": 1250000,
        "total_commissions": 125000.50,
        "total_payouts": 98000.25
    },
    "growth": {
        "new_users_this_month": 45,
        "clicks_growth": 15.2,
        "commission_growth": 22.1
    },
    "top_programs": [
        {
            "program_id": "prog_123",
            "name": "Nike",
            "total_clicks": 125000,
            "total_commissions": 12500.50
        }
    ]
}
```

## 🔧 FastAPI Implementation Structure

### Main Application Structure
```python
# main.py
from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer
import uvicorn

from routers import (
    auth, users, networks, programs, enrollments,
    campaigns, links, analytics, commissions, payouts,
    webhooks, api_keys, admin
)
from middleware import rate_limit, authentication
from database import engine, Base

# Create tables
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Affiliate Middleware Platform API",
    description="API for affiliate marketing middleware platform",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Middleware
app.add_middleware(CORSMiddleware, allow_origins=["*"])
app.add_middleware(rate_limit.RateLimitMiddleware)

# Security
security = HTTPBearer()

# Include routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(users.router, prefix="/api/v1/users", tags=["Users"])
app.include_router(networks.router, prefix="/api/v1/networks", tags=["Networks"])
app.include_router(programs.router, prefix="/api/v1/programs", tags=["Programs"])
app.include_router(enrollments.router, prefix="/api/v1/enrollments", tags=["Enrollments"])
app.include_router(campaigns.router, prefix="/api/v1/campaigns", tags=["Campaigns"])
app.include_router(links.router, prefix="/api/v1/links", tags=["Tracking Links"])
app.include_router(analytics.router, prefix="/api/v1/analytics", tags=["Analytics"])
app.include_router(commissions.router, prefix="/api/v1/commissions", tags=["Commissions"])
app.include_router(payouts.router, prefix="/api/v1/payouts", tags=["Payouts"])
app.include_router(webhooks.router, prefix="/api/v1/webhooks", tags=["Webhooks"])
app.include_router(api_keys.router, prefix="/api/v1/api-keys", tags=["API Keys"])
app.include_router(admin.router, prefix="/api/v1/admin", tags=["Admin"])

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### Dependencies and Authentication
```python
# dependencies.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
from database import get_db
from models import User
import jwt

security = HTTPBearer()

async def get_current_user(
    token: str = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    try:
        payload = jwt.decode(token.credentials, SECRET_KEY, algorithms=["HS256"])
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")

        user = db.query(User).filter(User.id == user_id).first()
        if user is None:
            raise HTTPException(status_code=401, detail="User not found")

        return user
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

async def get_admin_user(current_user: User = Depends(get_current_user)) -> User:
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")
    return current_user
```

## 🔗 Integration with Advanced Features

### **External API Integrations**
All affiliate network integrations are handled through the abstract integration system:
- See `BACKEND_EXTERNAL_API_INTEGRATIONS.md` for implementation details
- Automatic data synchronization via background jobs
- Standardized data formats across all networks

### **Real-time Features**
WebSocket endpoints provide live updates:
- See `BACKEND_REALTIME_FEATURES.md` for WebSocket implementation
- Real-time click tracking and commission notifications
- Live dashboard analytics updates

### **Background Processing**
Heavy operations are handled asynchronously:
- See `BACKEND_BACKGROUND_JOBS.md` for Celery task implementation
- Report generation, webhook delivery, data synchronization
- Email notifications and periodic maintenance

### **Deployment**
Complete containerization setup:
- See `BACKEND_DOCKER_DEPLOYMENT.md` for deployment configuration
- Development and production environments
- Scalable multi-service architecture

## 🚀 Implementation Steps

### **Phase 1: Core Setup**
1. **Database Setup** - Use `BACKEND_DATABASE_SETUP.md`
2. **Test Data** - Populate with `BACKEND_TEST_DATA.md`
3. **Docker Environment** - Setup with `BACKEND_DOCKER_DEPLOYMENT.md`

### **Phase 2: API Implementation**
1. **SQLAlchemy Models** - Based on database schema
2. **Pydantic Schemas** - Request/response validation
3. **Router Functions** - Implement each endpoint from this specification
4. **Authentication Middleware** - JWT and API key support

### **Phase 3: Advanced Features**
1. **External Integrations** - Implement affiliate network APIs
2. **Background Tasks** - Setup Celery for async processing
3. **Real-time Features** - Add WebSocket support
4. **Rate Limiting** - Redis-based request throttling

### **Phase 4: Production Ready**
1. **Testing** - Comprehensive test suite with pytest
2. **Logging and Monitoring** - Structured logging and health checks
3. **Caching** - Redis caching for performance
4. **Security** - Security headers, CORS, input validation
5. **Documentation** - Auto-generated OpenAPI docs

## 📚 Related Documentation

### **Core Documentation**
- `COMPREHENSIVE_ENTITIES_DOCUMENTATION.md` - Complete system overview
- `DATABASE_SCHEMA_SUMMARY.md` - Existing schema reference
- `BACKEND_DATABASE_SETUP.md` - Database setup and configuration

### **Advanced Features**
- `BACKEND_EXTERNAL_API_INTEGRATIONS.md` - Affiliate network integrations
- `BACKEND_REALTIME_FEATURES.md` - WebSocket real-time features
- `BACKEND_BACKGROUND_JOBS.md` - Celery background processing
- `BACKEND_DOCKER_DEPLOYMENT.md` - Complete deployment setup

### **Data Management**
- `BACKEND_TEST_DATA.md` - Test data generation and management

## 🎯 API Design Principles

### **RESTful Design**
- Clear resource-based URLs
- Proper HTTP methods and status codes
- Consistent response formats
- Comprehensive error handling

### **Security First**
- JWT authentication with refresh tokens
- API key management for developers
- Rate limiting and request throttling
- Input validation and sanitization

### **Performance Optimized**
- Async/await throughout
- Database query optimization
- Redis caching for frequent data
- Background processing for heavy operations

### **Developer Friendly**
- Auto-generated OpenAPI documentation
- Comprehensive examples and schemas
- Clear error messages and codes
- Consistent naming conventions

This API specification provides the complete foundation for building a production-ready affiliate middleware platform with all advanced features integrated.
