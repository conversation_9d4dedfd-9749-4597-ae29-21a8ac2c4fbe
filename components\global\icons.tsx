import {
  AlertTriangleIcon,
  ArchiveIcon,
  ArrowDownIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  ArrowUpDownIcon,
  ArrowUpIcon,
  AtSignIcon,
  Bell,
  Blocks,
  BriefcaseBusinessIcon,
  Brush,
  BuildingIcon,
  Calendar,
  ChartNoAxesColumn,
  ChartNoAxesCombinedIcon,
  CheckCircleIcon,
  CheckIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronsUpDownIcon,
  ChevronUpIcon,
  CircleIcon,
  ClockIcon,
  CodeIcon,
  CogIcon,
  ComponentIcon,
  CopyIcon,
  CornerDownRightIcon,
  CreditCard,
  DatabaseIcon,
  Edit2Icon,
  EditIcon,
  ExternalLinkIcon,
  EyeIcon,
  EyeOffIcon,
  FingerprintIcon,
  FolderGitIcon,
  FolderIcon,
  FormInputIcon,
  FrownIcon,
  GridIcon,
  HashIcon,
  HelpCircleIcon,
  HomeIcon,
  InfinityIcon,
  InfoIcon,
  LaptopIcon,
  Layers,
  LayoutDashboardIcon,
  LayoutIcon,
  LifeBuoy,
  LinkIcon,
  ListChecksIcon,
  LoaderIcon,
  LockIcon,
  LogOutIcon,
  MailIcon,
  MapIcon,
  MessageSquare,
  MinusIcon,
  MoonIcon,
  MoreHorizontal,
  PackageOpenIcon,
  PaperclipIcon,
  PencilIcon,
  PlusIcon,
  ReceiptIcon,
  RefreshCcw,
  RocketIcon,
  RouteIcon,
  SearchIcon,
  Send,
  ServerIcon,
  ShieldCheck,
  ShieldIcon,
  SidebarIcon,
  SlidersHorizontalIcon,
  SmartphoneIcon,
  SparklesIcon,
  SquareMousePointerIcon,
  SunIcon,
  TabletIcon,
  TextIcon,
  TimerIcon,
  Trash2Icon,
  UnplugIcon,
  UploadIcon,
  UserCircleIcon,
  UserIcon,
  UserMinusIcon,
  UserPlusIcon,
  UsersIcon,
  UserXIcon,
  WandSparklesIcon,
  XCircleIcon,
  XIcon,
  ZapIcon,
} from "lucide-react"
import { BsStripe } from "react-icons/bs"
import {
  FaFacebook,
  FaGithub,
  FaInstagram,
  FaLinkedin,
  FaPinterest,
  FaPinterestP,
  FaSquareXTwitter,
  FaTiktok,
  FaX,
  FaYoutube,
} from "react-icons/fa6"
import { TbArrowsSort } from "react-icons/tb"

type lucideIconProps = {
  size?: number
  color?: string
  strokeWidth?: number
  absoluteStrokeWidth?: boolean
}

export type Icon = lucideIconProps

export const Icons = {
  users: UsersIcon,
  bell: Bell,
  brush: Brush,
  linkedIn: FaLinkedin,
  tiktok: FaTiktok,
  youtube: FaYoutube,
  at: AtSignIcon,
  pinterest: FaPinterest,
  facebook: FaFacebook,
  settings: CogIcon,
  layers: Layers,
  sort: TbArrowsSort,
  xLogo: FaX,
  support: LifeBuoy,
  calendar: Calendar,
  send: Send,
  creditCard: CreditCard,
  blocks: Blocks,
  chart: ChartNoAxesColumn,
  loader: LoaderIcon,
  chevronLeft: ChevronLeftIcon,
  externalLink: ExternalLinkIcon,
  pintrest: FaPinterestP,
  plus: PlusIcon,
  trash: Trash2Icon,
  edit: EditIcon,
  actions: MoreHorizontal,
  search: SearchIcon,
  sun: SunIcon,
  moon: MoonIcon,
  laptop: LaptopIcon,
  cornerDownRight: CornerDownRightIcon,
  rocket: RocketIcon,
  logout: LogOutIcon,
  map: MapIcon,
  userCircle: UserCircleIcon,
  helpCircle: HelpCircleIcon,
  chevronsUpDown: ChevronsUpDownIcon,
  arrowRight: ArrowRightIcon,
  alertTriangle: AlertTriangleIcon,
  twitter: FaSquareXTwitter,
  checkCircle: CheckCircleIcon,
  check: CheckIcon,
  copy: CopyIcon,
  upload: UploadIcon,
  text: TextIcon,
  stripe: BsStripe,
  archive: ArchiveIcon,
  paperclip: PaperclipIcon,
  instagram: FaInstagram,
  minus: MinusIcon,
  userX: UserXIcon,
  user: UserIcon,
  x: XIcon,
  eyeOff: EyeOffIcon,
  eye: EyeIcon,
  info: InfoIcon,
  frown: FrownIcon,
  grid: GridIcon,
  refresh: RefreshCcw,
  chevronRight: ChevronRightIcon,
  circle: CircleIcon,
  tablet: TabletIcon,
  packageOpen: PackageOpenIcon,
  messageSquare: MessageSquare,
  link: LinkIcon,
  arrowUp: ArrowUpIcon,
  arrowDown: ArrowDownIcon,
  arrowUpDown: ArrowUpDownIcon,
  SlidersHorizontal: SlidersHorizontalIcon,
  folder: FolderIcon,
  hash: HashIcon,
  listCheck: ListChecksIcon,
  phone: SmartphoneIcon,
  chevronUp: ChevronUpIcon,
  chevronDown: ChevronDownIcon,
  warning: AlertTriangleIcon,
  workspace: BuildingIcon,
  wand: WandSparklesIcon,
  connect: UnplugIcon,
  shield: ShieldIcon,
  lock: LockIcon,
  database: DatabaseIcon,
  shieldCheck: ShieldCheck,
  infinity: InfinityIcon,
  zap: ZapIcon,
  clock: ClockIcon,
  server: ServerIcon,
  git: FolderGitIcon,
  userPlus: UserPlusIcon,
  mail: MailIcon,
  receipt: ReceiptIcon,
  timer: TimerIcon,
  briefcase: BriefcaseBusinessIcon,
  layout: LayoutIcon,
  code: CodeIcon,
  graph: ChartNoAxesCombinedIcon,
  component: ComponentIcon,
  formInput: FormInputIcon,
  route: RouteIcon,
  sidebar: SidebarIcon,
  github: FaGithub,
  home: HomeIcon,
  arrowLeft: ArrowLeftIcon,
  dashboard: LayoutDashboardIcon,
  fingerprint: FingerprintIcon,
  sparkles: SparklesIcon,
  pencil: PencilIcon,
  userMinus: UserMinusIcon,
  xCircle: XCircleIcon,
  edit2: Edit2Icon,
  squareMousePointer: SquareMousePointerIcon,
}
