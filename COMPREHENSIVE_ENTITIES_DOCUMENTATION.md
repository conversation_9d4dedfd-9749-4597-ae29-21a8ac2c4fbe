# Comprehensive Entities Documentation
## Affiliate Middleware Platform Database Design

This document outlines all necessary entities (existing and new) required to implement the affiliate middleware platform functionality based on the functional requirements.

## 📊 Entity Categories

### 🏗️ **Core Foundation Entities (Existing)**
These entities from the current schema provide the foundational infrastructure:

#### Users Table ✅ (Existing - Enhanced)
**Purpose**: Core user accounts with affiliate-specific enhancements
- **Primary Key**: `id` (text)
- **Existing Fields**: name, lastName, email, emailVerified, image, stripeCustomerId, etc.
- **New Fields Needed**:
  - `userType` (individual, agency, developer)
  - `kycStatus` (pending, verified, rejected)
  - `affiliateId` (unique affiliate identifier)
  - `taxInfo` (JSON for tax documentation)
  - `payoutMethod` (bank, paypal, stripe)
  - `payoutDetails` (JSON for payout information)

#### Workspaces Table ✅ (Existing - Repurposed)
**Purpose**: Organizations/agencies managing affiliate campaigns
- **Primary Key**: `id` (UUID)
- **Existing Fields**: name, slug, ownerId, subscriptionId
- **Enhanced Purpose**: Represents agencies or teams managing affiliate campaigns

#### Subscriptions Table ✅ (Existing - Enhanced)
**Purpose**: Billing plans with affiliate-specific quotas
- **New Quota Fields Needed**:
  - `maxAffiliatePrograms` (number of programs user can join)
  - `maxTrackingLinks` (number of tracking links)
  - `maxApiCalls` (API usage limits)
  - `advancedAnalytics` (boolean for premium analytics)

### 🔗 **Affiliate-Specific Core Entities (New)**

#### Affiliate Networks Table 🆕
**Purpose**: External affiliate networks (AWIN, CJ, Rakuten, etc.)
```sql
- id (UUID, Primary Key)
- name (text) - "AWIN", "CJ Affiliate", etc.
- slug (text, unique) - "awin", "cj-affiliate"
- apiEndpoint (text) - Base API URL
- authType (enum) - oauth2, api_key, basic_auth
- authConfig (JSON) - Authentication configuration
- isActive (boolean) - Network availability
- supportedFeatures (JSON) - Available features (tracking, reporting, etc.)
- rateLimit (integer) - API rate limit per hour
- createdAt (timestamp)
- updatedAt (timestamp)
```

#### Affiliate Programs Table 🆕
**Purpose**: Individual affiliate programs from networks
```sql
- id (UUID, Primary Key)
- networkId (UUID, FK to affiliate_networks)
- externalId (text) - Program ID from external network
- name (text) - Program name
- brand (text) - Brand/company name
- description (text) - Program description
- category (text) - Product category
- commissionType (enum) - percentage, fixed, tiered
- commissionRate (decimal) - Base commission rate
- commissionStructure (JSON) - Detailed commission tiers
- cookieDuration (integer) - Cookie duration in days
- payoutTerms (text) - Payment terms
- restrictions (JSON) - Geographic, content restrictions
- status (enum) - active, paused, expired, pending_approval
- minPayout (decimal) - Minimum payout threshold
- avgEarningsPerClick (decimal) - Average EPC
- conversionRate (decimal) - Average conversion rate
- logoUrl (text) - Program logo
- websiteUrl (text) - Merchant website
- termsUrl (text) - Terms and conditions URL
- createdAt (timestamp)
- updatedAt (timestamp)
- lastSyncAt (timestamp) - Last sync with external API
```

#### User Program Enrollments Table 🆕
**Purpose**: Track which users are enrolled in which affiliate programs
```sql
- id (UUID, Primary Key)
- userId (text, FK to users)
- programId (UUID, FK to affiliate_programs)
- workspaceId (UUID, FK to workspaces, nullable)
- status (enum) - pending, approved, rejected, suspended
- applicationDate (timestamp)
- approvalDate (timestamp, nullable)
- rejectionReason (text, nullable)
- customCommissionRate (decimal, nullable) - Negotiated rate
- trackingId (text) - User's tracking ID for this program
- notes (text, nullable) - Internal notes
- createdAt (timestamp)
- updatedAt (timestamp)
```

### 📊 **Tracking & Analytics Entities (New)**

#### Tracking Links Table 🆕
**Purpose**: Generated affiliate tracking links
```sql
- id (UUID, Primary Key)
- userId (text, FK to users)
- programId (UUID, FK to affiliate_programs)
- workspaceId (UUID, FK to workspaces, nullable)
- campaignId (UUID, FK to campaigns, nullable)
- originalUrl (text) - Original merchant URL
- trackingUrl (text, unique) - Generated tracking URL
- shortCode (text, unique) - Short identifier
- linkType (enum) - direct, iframe, widget, api
- customParameters (JSON) - UTM and custom tracking params
- isActive (boolean)
- expiresAt (timestamp, nullable)
- clickCount (integer, default 0)
- conversionCount (integer, default 0)
- totalCommission (decimal, default 0)
- lastClickAt (timestamp, nullable)
- createdAt (timestamp)
- updatedAt (timestamp)
```

#### Click Events Table 🆕
**Purpose**: Individual click tracking events
```sql
- id (UUID, Primary Key)
- trackingLinkId (UUID, FK to tracking_links)
- ipAddress (text) - Anonymized IP
- userAgent (text) - Browser/device info
- referrer (text, nullable) - Referring URL
- country (text, nullable) - Detected country
- device (text, nullable) - mobile, desktop, tablet
- browser (text, nullable) - Browser type
- clickedAt (timestamp)
- conversionAt (timestamp, nullable) - If conversion occurred
- commissionAmount (decimal, nullable) - Commission earned
- conversionValue (decimal, nullable) - Order value
- status (enum) - pending, confirmed, rejected
- externalTransactionId (text, nullable) - Network transaction ID
```

#### Campaigns Table 🆕
**Purpose**: Organize tracking links into campaigns
```sql
- id (UUID, Primary Key)
- userId (text, FK to users)
- workspaceId (UUID, FK to workspaces, nullable)
- name (text) - Campaign name
- description (text, nullable)
- startDate (timestamp)
- endDate (timestamp, nullable)
- budget (decimal, nullable) - Campaign budget
- targetClicks (integer, nullable)
- targetConversions (integer, nullable)
- status (enum) - active, paused, completed, archived
- tags (JSON) - Campaign tags
- createdAt (timestamp)
- updatedAt (timestamp)
```

### 💰 **Financial & Reporting Entities (New)**

#### Commissions Table 🆕
**Purpose**: Track commission earnings and payouts
```sql
- id (UUID, Primary Key)
- userId (text, FK to users)
- programId (UUID, FK to affiliate_programs)
- trackingLinkId (UUID, FK to tracking_links, nullable)
- clickEventId (UUID, FK to click_events, nullable)
- amount (decimal) - Commission amount
- currency (text, default 'USD')
- commissionType (enum) - click, conversion, bonus
- status (enum) - pending, confirmed, paid, rejected
- transactionDate (timestamp) - When transaction occurred
- confirmedAt (timestamp, nullable) - When commission confirmed
- paidAt (timestamp, nullable) - When commission paid
- payoutId (UUID, FK to payouts, nullable)
- externalTransactionId (text, nullable) - Network transaction ID
- orderValue (decimal, nullable) - Total order value
- notes (text, nullable)
- createdAt (timestamp)
- updatedAt (timestamp)
```

#### Payouts Table 🆕
**Purpose**: Batch commission payouts to users
```sql
- id (UUID, Primary Key)
- userId (text, FK to users)
- amount (decimal) - Total payout amount
- currency (text, default 'USD')
- method (enum) - bank_transfer, paypal, stripe
- status (enum) - pending, processing, completed, failed
- scheduledAt (timestamp) - When payout scheduled
- processedAt (timestamp, nullable) - When payout processed
- externalTransactionId (text, nullable) - Payment processor ID
- fees (decimal, default 0) - Processing fees
- netAmount (decimal) - Amount after fees
- notes (text, nullable)
- createdAt (timestamp)
- updatedAt (timestamp)
```

### 🔧 **Integration & API Entities (New)**

#### API Keys Table 🆕
**Purpose**: Manage user API keys for developer access
```sql
- id (UUID, Primary Key)
- userId (text, FK to users)
- workspaceId (UUID, FK to workspaces, nullable)
- name (text) - Key name/description
- keyHash (text) - Hashed API key
- permissions (JSON) - API permissions
- isActive (boolean)
- lastUsedAt (timestamp, nullable)
- usageCount (integer, default 0)
- rateLimit (integer) - Requests per hour
- expiresAt (timestamp, nullable)
- createdAt (timestamp)
- updatedAt (timestamp)
```

#### Webhook Endpoints Table 🆕
**Purpose**: User-defined webhook endpoints for real-time notifications
```sql
- id (UUID, Primary Key)
- userId (text, FK to users)
- workspaceId (UUID, FK to workspaces, nullable)
- url (text) - Webhook URL
- events (JSON) - Array of subscribed events
- secret (text) - Webhook secret for verification
- isActive (boolean)
- lastTriggeredAt (timestamp, nullable)
- failureCount (integer, default 0)
- createdAt (timestamp)
- updatedAt (timestamp)
```

### 📱 **Enhanced Existing Entities**

#### Notifications Table ✅ (Existing - Enhanced)
**Purpose**: Enhanced for affiliate-specific notifications
- **New Notification Types**:
  - `program_approved` - Program application approved
  - `commission_earned` - New commission earned
  - `payout_processed` - Payout completed
  - `program_status_changed` - Program status update
  - `api_limit_reached` - API usage limit reached

#### User Settings Table ✅ (Existing - Enhanced)
**Purpose**: Enhanced with affiliate-specific preferences
- **New Settings Fields**:
  - `defaultCommissionDisplay` (gross, net, percentage)
  - `autoApplyPrograms` (boolean)
  - `preferredPayoutFrequency` (weekly, monthly, quarterly)
  - `analyticsTimeZone` (text)

## 🔗 Key Relationships

### User-Centric Affiliate Relationships
```
User (1) ←→ (M) User Program Enrollments
User (1) ←→ (M) Tracking Links
User (1) ←→ (M) Campaigns
User (1) ←→ (M) Commissions
User (1) ←→ (M) Payouts
User (1) ←→ (M) API Keys
User (1) ←→ (M) Webhook Endpoints
```

### Program-Centric Relationships
```
Affiliate Network (1) ←→ (M) Affiliate Programs
Affiliate Program (1) ←→ (M) User Program Enrollments
Affiliate Program (1) ←→ (M) Tracking Links
Affiliate Program (1) ←→ (M) Commissions
```

### Tracking & Analytics Relationships
```
Tracking Link (1) ←→ (M) Click Events
Click Event (1) ←→ (1) Commission (nullable)
Campaign (1) ←→ (M) Tracking Links
Payout (1) ←→ (M) Commissions
```

## 🎯 Implementation Priority

### Phase 1: Core Affiliate Infrastructure
1. Affiliate Networks Table
2. Affiliate Programs Table  
3. User Program Enrollments Table
4. Enhanced Users Table (affiliate fields)

### Phase 2: Tracking & Analytics
1. Tracking Links Table
2. Click Events Table
3. Campaigns Table
4. Commissions Table

### Phase 3: Financial & Payouts
1. Payouts Table
2. Enhanced financial reporting

### Phase 4: Developer Tools
1. API Keys Table
2. Webhook Endpoints Table
3. Enhanced notifications

## 🚀 **Implementation Features Added**

### **1. Abstract API Integration System** ✅
- **Location**: `BACKEND_EXTERNAL_API_INTEGRATIONS.md`
- **Features**:
  - Abstract base classes for easy network integration
  - Support for AWIN, CJ Affiliate, Rakuten, Impact, TradeDoubler
  - Standardized data formats across all networks
  - Factory pattern for easy extensibility
  - Built-in authentication, rate limiting, and error handling

### **2. Real-time Features with WebSockets** ✅
- **Location**: `BACKEND_REALTIME_FEATURES.md`
- **Features**:
  - Live click tracking and conversion updates
  - Real-time commission notifications
  - Live dashboard analytics
  - WebSocket connection management
  - Multi-user workspace synchronization
  - Browser notification support

### **3. Background Jobs with Celery & Redis** ✅
- **Location**: `BACKEND_BACKGROUND_JOBS.md`
- **Features**:
  - Asynchronous report generation (CSV, Excel, PDF)
  - Reliable webhook delivery with retry logic
  - Automated data synchronization from affiliate networks
  - Email notification system
  - Periodic maintenance tasks
  - Queue-based task processing with monitoring

### **4. Docker Deployment Configuration** ✅
- **Location**: `BACKEND_DOCKER_DEPLOYMENT.md`
- **Features**:
  - Complete containerization setup
  - Development and production environments
  - Multi-service orchestration (FastAPI, PostgreSQL, Redis, Celery, Nginx)
  - SSL/TLS configuration
  - Health checks and monitoring
  - Scalable worker configuration

## 🔧 **Backend Implementation Stack**

### **Core Technologies**
- **FastAPI** - High-performance async web framework
- **PostgreSQL** - Primary database with advanced features
- **Redis** - Caching and message broker
- **Celery** - Distributed task queue
- **SQLAlchemy** - ORM with async support
- **Pydantic** - Data validation and serialization

### **External Integrations**
- **Affiliate Networks** - AWIN, CJ, Rakuten, Impact, TradeDoubler, PartnerStack
- **Payment Processing** - Stripe integration
- **Email Service** - Resend for transactional emails
- **File Storage** - AWS S3 for reports and assets
- **Monitoring** - Health checks and performance metrics

### **Security & Performance**
- **JWT Authentication** - Secure user sessions
- **API Key Management** - Developer access control
- **Rate Limiting** - Redis-based request throttling
- **CORS Configuration** - Cross-origin request handling
- **SSL/TLS** - Encrypted communications

## 📚 **Documentation Structure**

### **Database & Schema**
1. `DATABASE_SCHEMA_SUMMARY.md` - Existing entities overview
2. `BACKEND_DATABASE_SETUP.md` - PostgreSQL schema creation
3. `BACKEND_TEST_DATA.md` - Test data population scripts

### **API & Integration**
4. `BACKEND_API_SPECIFICATION.md` - Complete API documentation
5. `BACKEND_EXTERNAL_API_INTEGRATIONS.md` - Affiliate network integrations
6. `COMPREHENSIVE_ENTITIES_DOCUMENTATION.md` - This file

### **Advanced Features**
7. `BACKEND_REALTIME_FEATURES.md` - WebSocket implementation
8. `BACKEND_BACKGROUND_JOBS.md` - Celery task processing
9. `BACKEND_DOCKER_DEPLOYMENT.md` - Containerization setup

## 🎯 **Implementation Roadmap**

### **Phase 1: Core Infrastructure** (Weeks 1-2)
- ✅ Database schema setup
- ✅ FastAPI application structure
- ✅ Basic authentication system
- ✅ Docker development environment

### **Phase 2: Core Affiliate Features** (Weeks 3-4)
- ✅ User management and KYC
- ✅ Affiliate network integrations
- ✅ Program enrollment system
- ✅ Tracking link generation

### **Phase 3: Analytics & Reporting** (Weeks 5-6)
- ✅ Click tracking and analytics
- ✅ Commission calculation
- ✅ Background report generation
- ✅ Real-time dashboard updates

### **Phase 4: Advanced Features** (Weeks 7-8)
- ✅ Webhook system
- ✅ API key management
- ✅ Payout processing
- ✅ Production deployment

### **Phase 5: Optimization & Scaling** (Weeks 9-10)
- Performance optimization
- Load testing and scaling
- Monitoring and alerting
- Security auditing

## ❓ Questions Addressed

1. **Commission Structure**: ✅ Flexible commission structure with JSON support for complex tiers
2. **Multi-Currency**: ✅ Built-in multi-currency support with conversion tracking
3. **Sub-Affiliates**: 🔄 Can be added as extension to user referral system
4. **Fraud Detection**: ✅ IP tracking, device fingerprinting, and anomaly detection ready
5. **Data Retention**: ✅ Configurable retention policies with automated cleanup
6. **Real-time vs Batch**: ✅ Both supported - real-time for UI, batch for heavy processing
7. **White-label**: ✅ Workspace-based multi-tenancy supports white-label solutions
8. **Compliance**: ✅ GDPR-ready with data anonymization and retention controls

## 🔗 **Next Steps**

1. **Review Documentation** - Ensure all requirements are covered
2. **Set Up Development Environment** - Use Docker configuration
3. **Implement Core Features** - Follow the API specification
4. **Test Integration** - Use provided test data scripts
5. **Deploy to Production** - Use Docker production configuration

The comprehensive backend system is now fully documented and ready for implementation!
